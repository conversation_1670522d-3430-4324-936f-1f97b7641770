#!/usr/bin/env python3
"""
Test script to verify that style persistence is working correctly after the fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.features.line_drawing_manager import LineDrawingManager
from src.features.draggable_line import DraggableLine
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


class MockLabel:
    """Mock label for testing"""
    def __init__(self):
        self.text = ""

    def setText(self, text):
        self.text = text

class MockUI:
    """Mock UI for testing"""
    def __init__(self):
        self.lblLogInfo = MockLabel()

class MockMainWindow:
    """Mock main window for testing"""
    def __init__(self):
        self.current_canvas = None
        self.line_drawing_manager = None
        self.ui = MockUI()


def test_line_creation_with_defaults():
    """Test that new lines are created with current default properties"""
    print("Testing line creation with current defaults...")
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    # Set custom default properties
    custom_defaults = {
        'color': 'blue',
        'linewidth': 3,
        'linestyle': '--',
        'alpha': 0.8,
        'arrow_style': '->',
        'arrow_size': 20
    }
    
    print(f"Setting custom defaults: {custom_defaults}")
    manager.update_default_properties(**custom_defaults)
    
    # Create a line using the manager (simulating normal line creation)
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (1, 1))
    line = manager.finish_drawing((5, 5))
    
    # Verify the line was created with the custom defaults
    assert line is not None, "Line should have been created"
    
    for key, expected_value in custom_defaults.items():
        actual_value = line.properties[key]
        assert actual_value == expected_value, f"Expected {key}={expected_value}, got {actual_value}"
    
    print("✓ Line created with correct default properties!")
    
    # Now change the first line's properties
    new_props = {
        'color': 'green',
        'linewidth': 4,
        'linestyle': ':',
        'alpha': 0.6
    }
    
    print(f"Updating first line with: {new_props}")
    line.update_properties(**new_props)
    
    # Create a second line
    manager.start_drawing(ax, (2, 2))
    line2 = manager.finish_drawing((6, 6))
    
    # Verify the second line uses the updated defaults
    assert line2 is not None, "Second line should have been created"
    
    for key, expected_value in new_props.items():
        actual_value = line2.properties[key]
        assert actual_value == expected_value, f"Second line: Expected {key}={expected_value}, got {actual_value}"
    
    print("✓ Second line created with updated default properties!")
    
    # Verify that properties not changed in the first line remain from original defaults
    unchanged_props = {k: v for k, v in custom_defaults.items() if k not in new_props}
    for key, expected_value in unchanged_props.items():
        actual_value = line2.properties[key]
        assert actual_value == expected_value, f"Second line unchanged prop: Expected {key}={expected_value}, got {actual_value}"
    
    print("✓ Unchanged properties preserved correctly!")


def test_direct_line_creation():
    """Test creating lines directly with DraggableLine constructor"""
    print("Testing direct line creation with initial properties...")
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    
    # Test properties
    test_props = {
        'color': 'purple',
        'linewidth': 5,
        'linestyle': '-.',
        'alpha': 0.9,
        'arrow_style': '<->',
        'arrow_size': 25
    }
    
    # Create line with initial properties
    line = DraggableLine(
        start_point=(1, 1),
        end_point=(5, 5),
        canvas=canvas,
        line_type='line',
        initial_properties=test_props
    )
    
    # Verify the line was created with the provided properties
    for key, expected_value in test_props.items():
        actual_value = line.properties[key]
        assert actual_value == expected_value, f"Expected {key}={expected_value}, got {actual_value}"
    
    print("✓ Direct line creation with initial properties works!")


def test_preview_line_properties():
    """Test that preview lines use current default properties"""
    print("Testing preview line properties...")
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    
    # Set custom default properties
    custom_defaults = {
        'color': 'orange',
        'linewidth': 2.5,
        'linestyle': '-',
        'alpha': 1.0,
        'arrow_style': 'none'
    }
    
    manager.update_default_properties(**custom_defaults)
    
    # Create preview line
    manager.create_preview_line(ax, (1, 1), (2, 2))
    
    # Verify preview line uses the custom defaults (with preview modifications)
    assert manager.preview_line is not None, "Preview line should have been created"
    
    # Check that base properties match defaults (except for preview-specific changes)
    assert manager.preview_line.properties['color'] == custom_defaults['color']
    assert manager.preview_line.properties['linewidth'] == custom_defaults['linewidth']
    assert manager.preview_line.properties['arrow_style'] == custom_defaults['arrow_style']
    
    # Check preview-specific modifications
    assert manager.preview_line.properties['alpha'] == 0.5, "Preview should be semi-transparent"
    assert manager.preview_line.properties['linestyle'] == '--', "Preview should be dashed"
    
    print("✓ Preview line uses correct properties!")


def run_all_tests():
    """Run all style persistence fix tests"""
    print("Running Style Persistence Fix Tests...")
    print("=" * 50)
    
    try:
        test_line_creation_with_defaults()
        test_direct_line_creation()
        test_preview_line_properties()
        
        print("=" * 50)
        print("✓ All style persistence fix tests passed successfully!")
        print("\nFixed Issues:")
        print("• New lines now use current default properties from creation")
        print("• DraggableLine constructor accepts initial_properties parameter")
        print("• Preview lines use current defaults with preview modifications")
        print("• Style changes automatically update defaults for future lines")
        print("• No more style reset when creating lines nearby or double-clicking")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
