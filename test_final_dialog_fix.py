#!/usr/bin/env python3
"""
Final test to verify all dialog and dragging issues are resolved
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.features.line_drawing_manager import LineDrawingManager
from src.features.draggable_line import DraggableLine
from gui.dialog.line_settings_dialog import LineSettingsDialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


class MockLabel:
    def __init__(self):
        self.text = ""
    def setText(self, text):
        self.text = text

class MockUI:
    def __init__(self):
        self.lblLogInfo = MockLabel()

class MockMainWindow:
    def __init__(self):
        self.current_canvas = None
        self.line_drawing_manager = None
        self.ui = MockUI()


def test_complete_user_workflow():
    """Test the complete user workflow with all fixes"""
    print("Testing complete user workflow with fixes...")
    print("=" * 60)
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    print("Step 1: Create a line")
    
    # Create a line
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (1, 1))
    line1 = manager.finish_drawing((5, 5))
    
    initial_props = line1.get_properties()
    print(f"Initial properties: {initial_props}")
    
    print("\nStep 2: Apply style through dialog")
    
    # Create and configure dialog
    dialog1 = LineSettingsDialog(parent=None, line_manager=manager, current_line=line1)
    
    # Set new style
    dialog1.color_edit.setText("#FF0000")  # Red
    dialog1.width_spin.setValue(3.0)       # Thickness 3
    dialog1.style_combo.setCurrentIndex(1) # Dashed
    
    # Apply settings
    dialog1.apply_settings()
    
    props_after_dialog = line1.get_properties()
    print(f"Properties after dialog: {props_after_dialog}")
    
    # Verify changes
    assert props_after_dialog['color'] == '#FF0000'
    assert props_after_dialog['linewidth'] == 3.0
    assert props_after_dialog['linestyle'] == '--'
    
    print("✅ Dialog applied changes correctly")
    
    print("\nStep 3: Reopen dialog and verify it shows current properties")
    
    # Create new dialog instance
    dialog2 = LineSettingsDialog(parent=None, line_manager=manager, current_line=line1)
    
    # Check loaded values
    loaded_color = dialog2.color_edit.text()
    loaded_width = dialog2.width_spin.value()
    loaded_style = dialog2.style_combo.currentIndex()
    
    print(f"Dialog reopened with: Color={loaded_color}, Width={loaded_width}, Style={loaded_style}")
    
    # Verify dialog loaded current properties
    assert loaded_color == '#FF0000', f"Color mismatch: {loaded_color} != #FF0000"
    assert loaded_width == 3.0, f"Width mismatch: {loaded_width} != 3.0"
    assert loaded_style == 1, f"Style mismatch: {loaded_style} != 1"
    
    print("✅ Dialog correctly loaded current properties")
    
    print("\nStep 4: Test dragging preserves properties")
    
    props_before_drag = line1.get_properties().copy()
    
    # Simulate dragging
    line1.press = ((1, 1), 100, 100)
    line1.dragging_point = 'line'
    line1.connected = True
    line1._dragging = True
    
    # Simulate motion
    class MockEvent:
        def __init__(self, x, y):
            self.xdata = x
            self.ydata = y
    
    motion_event = MockEvent(2, 2)
    line1.handle_motion(motion_event)
    
    # End dragging
    line1.handle_release(motion_event)
    
    props_after_drag = line1.get_properties()
    print(f"Properties after drag: {props_after_drag}")
    
    # Verify properties unchanged
    for key in ['color', 'linewidth', 'linestyle', 'alpha']:
        assert props_before_drag[key] == props_after_drag[key], f"Drag changed {key}: {props_before_drag[key]} -> {props_after_drag[key]}"
    
    print("✅ Dragging preserved properties")
    
    print("\nStep 5: Verify dialog still works after dragging")
    
    # Create another dialog instance after dragging
    dialog3 = LineSettingsDialog(parent=None, line_manager=manager, current_line=line1)
    
    # Check loaded values after dragging
    loaded_color_after_drag = dialog3.color_edit.text()
    loaded_width_after_drag = dialog3.width_spin.value()
    loaded_style_after_drag = dialog3.style_combo.currentIndex()
    
    print(f"Dialog after drag: Color={loaded_color_after_drag}, Width={loaded_width_after_drag}, Style={loaded_style_after_drag}")
    
    # Verify dialog still loads correct properties
    assert loaded_color_after_drag == '#FF0000', f"After drag color: {loaded_color_after_drag} != #FF0000"
    assert loaded_width_after_drag == 3.0, f"After drag width: {loaded_width_after_drag} != 3.0"
    assert loaded_style_after_drag == 1, f"After drag style: {loaded_style_after_drag} != 1"
    
    print("✅ Dialog works correctly after dragging")
    
    print("\nStep 6: Test style persistence for new lines")
    
    # Create a new line
    manager.start_drawing(ax, (2, 2))
    line2 = manager.finish_drawing((6, 6))
    
    props_new_line = line2.get_properties()
    print(f"New line properties: {props_new_line}")
    
    # Verify new line uses updated defaults
    assert props_new_line['color'] == '#FF0000', f"New line color: {props_new_line['color']} != #FF0000"
    assert props_new_line['linewidth'] == 3.0, f"New line width: {props_new_line['linewidth']} != 3.0"
    assert props_new_line['linestyle'] == '--', f"New line style: {props_new_line['linestyle']} != --"
    
    print("✅ New line uses updated defaults")
    
    return True


def run_final_test():
    """Run the final comprehensive test"""
    print("Running Final Dialog and Dragging Fix Test...")
    print("=" * 70)
    
    try:
        result = test_complete_user_workflow()
        
        print("\n" + "=" * 70)
        if result:
            print("✅ ALL TESTS PASSED!")
            print("\nFixes implemented and verified:")
            print("• Dialog always loads current line properties")
            print("• Dialog refreshes when shown/reopened")
            print("• Dragging preserves line properties")
            print("• Style persistence works correctly")
            print("• No property resets during operations")
            print("\nThe dialog property issues should now be resolved!")
        else:
            print("❌ SOME TESTS FAILED!")
            print("Dialog property issues may still exist.")
        
        return result
        
    except Exception as e:
        print(f"❌ TEST FAILED WITH EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_final_test()
    sys.exit(0 if success else 1)
