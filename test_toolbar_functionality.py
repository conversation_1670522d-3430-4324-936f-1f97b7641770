#!/usr/bin/env python3
"""
Test script to verify the CustomNavigationToolbar functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_toolbar_initialization():
    """Test that the toolbar initializes correctly with all managers"""
    print("Testing toolbar initialization...")
    
    try:
        from gui.dialog.custom_plot_settings import CustomNavigationToolbar
        
        # Create a mock canvas
        class MockCanvas:
            def __init__(self):
                self.figure = MockFigure()
                self.mpl_connect_calls = []
                
            def mpl_connect(self, event_type, handler):
                self.mpl_connect_calls.append((event_type, handler))
                return len(self.mpl_connect_calls)  # Return a mock handler ID
                
            def mpl_disconnect(self, handler_id):
                pass  # Mock disconnect
        
        class MockFigure:
            def __init__(self):
                self.axes = [MockAxes()]
                
        class MockAxes:
            def __init__(self):
                pass
        
        class MockParent:
            def __init__(self):
                pass
                
            def mapToGlobal(self, point):
                return point
        
        # Create toolbar
        canvas = MockCanvas()
        parent = MockParent()
        data_frame = {'data_frame_1': {'test': [1, 2, 3]}}
        
        toolbar = CustomNavigationToolbar(
            canvas=canvas,
            parent=parent,
            data_frame=data_frame
        )
        
        print("✓ Toolbar created successfully")
        
        # Check managers
        if hasattr(toolbar, 'annotation_manager') and toolbar.annotation_manager:
            print("✓ AnnotationManager initialized")
        else:
            print("✗ AnnotationManager not initialized")
            return False
            
        if hasattr(toolbar, 'line_drawing_manager') and toolbar.line_drawing_manager:
            print("✓ LineDrawingManager initialized")
        else:
            print("✗ LineDrawingManager not initialized")
            return False
            
        if hasattr(toolbar, 'mock_main_window') and toolbar.mock_main_window:
            print("✓ Mock main window created")
        else:
            print("✗ Mock main window not created")
            return False
            
        # Check event handlers
        if hasattr(toolbar, 'event_handlers') and len(toolbar.event_handlers) > 0:
            print(f"✓ Event handlers connected: {len(toolbar.event_handlers)}")
        else:
            print("✗ Event handlers not connected")
            return False
            
        # Check that canvas received event connections
        expected_events = [
            'button_press_event',
            'motion_notify_event', 
            'button_release_event',
            'pick_event'
        ]
        
        connected_events = [event_type for event_type, _ in canvas.mpl_connect_calls]
        for event_type in expected_events:
            if event_type in connected_events:
                print(f"✓ {event_type} handler connected")
            else:
                print(f"✗ {event_type} handler missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Toolbar initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manager_functionality():
    """Test that the managers have the correct functionality"""
    print("\nTesting manager functionality...")
    
    try:
        from src.features.line_drawing_manager import LineDrawingManager
        from src.features.annotation_manager import AnnotationManager
        
        # Test LineDrawingManager
        line_manager = LineDrawingManager()
        
        # Test basic functionality
        line_manager.set_drawing_mode(True)
        if line_manager.is_drawing_mode_enabled():
            print("✓ Line drawing mode can be enabled")
        else:
            print("✗ Line drawing mode not working")
            return False
            
        line_manager.set_line_orientation('horizontal')
        if line_manager.get_line_orientation() == 'horizontal':
            print("✓ Line orientation setting works")
        else:
            print("✗ Line orientation setting failed")
            return False
        
        # Test AnnotationManager
        annotation_manager = AnnotationManager()
        
        annotation_manager.set_annotation_mode(True)
        if annotation_manager.is_annotation_mode_enabled():
            print("✓ Annotation mode can be enabled")
        else:
            print("✗ Annotation mode not working")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Manager functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_toggle_methods():
    """Test the toggle methods work correctly"""
    print("\nTesting toggle methods...")
    
    try:
        from gui.dialog.custom_plot_settings import CustomNavigationToolbar
        
        # Create mock objects
        class MockCanvas:
            def __init__(self):
                self.figure = MockFigure()
                
            def mpl_connect(self, event_type, handler):
                return 1  # Mock handler ID
                
            def mpl_disconnect(self, handler_id):
                pass
        
        class MockFigure:
            def __init__(self):
                self.axes = [MockAxes()]
                
        class MockAxes:
            pass
        
        class MockParent:
            def mapToGlobal(self, point):
                return point
        
        # Create toolbar
        toolbar = CustomNavigationToolbar(
            canvas=MockCanvas(),
            parent=MockParent(),
            data_frame={'data_frame_1': {'test': [1, 2, 3]}}
        )
        
        # Test annotation toggle
        if hasattr(toolbar, 'annotation_btn'):
            # Mock the button
            class MockButton:
                def __init__(self):
                    self.checked = False
                def isChecked(self):
                    return self.checked
                def setChecked(self, checked):
                    self.checked = checked
                def setToolTip(self, tip):
                    pass
                    
            toolbar.annotation_btn = MockButton()
            toolbar.line_btn = MockButton()
            
            # Test enabling annotation mode
            toolbar.annotation_btn.setChecked(True)
            toolbar.toggle_annotation_mode()
            
            if toolbar.annotation_manager.is_annotation_mode_enabled():
                print("✓ Annotation mode toggle works")
            else:
                print("✗ Annotation mode toggle failed")
                return False
                
            # Test enabling line mode
            toolbar.line_btn.setChecked(True)
            toolbar.toggle_line_drawing_mode()
            
            if toolbar.line_drawing_manager.is_drawing_mode_enabled():
                print("✓ Line drawing mode toggle works")
            else:
                print("✗ Line drawing mode toggle failed")
                return False
                
            # Test mutual exclusion
            if not toolbar.annotation_manager.is_annotation_mode_enabled():
                print("✓ Modes are mutually exclusive")
            else:
                print("✗ Modes are not mutually exclusive")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Toggle methods test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all functionality tests"""
    print("Testing CustomNavigationToolbar Functionality...")
    print("=" * 55)
    
    tests = [
        test_toolbar_initialization,
        test_manager_functionality,
        test_toggle_methods
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 55)
    if all_passed:
        print("✓ All functionality tests passed!")
        print("\nThe CustomNavigationToolbar should now work correctly:")
        print("• Managers are properly initialized")
        print("• Event handlers are connected")
        print("• Toggle methods work correctly")
        print("• Annotation and line tools are mutually exclusive")
        print("• Mock main window provides necessary interface")
        print("\nTry using the toolbar in your application!")
    else:
        print("✗ Some functionality tests failed.")
        print("Please check the errors above and verify the implementation.")
    
    return all_passed

if __name__ == "__main__":
    run_all_tests()
