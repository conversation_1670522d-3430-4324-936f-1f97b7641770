#!/usr/bin/env python3
"""
Test script to verify the custom plot settings line tool functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required imports work"""
    print("Testing imports...")
    
    try:
        from gui.dialog.custom_plot_settings import CustomNavigationToolbar, PlotSettingsManager
        print("✓ CustomNavigationToolbar imported successfully")
        
        from src.features import LineDrawingManager, AnnotationManager
        print("✓ LineDrawingManager and AnnotationManager imported successfully")
        
        from gui.dialog.custom_plot_settings import LineManager
        print("✓ LineManager imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_class_instantiation():
    """Test if classes can be instantiated"""
    print("\nTesting class instantiation...")
    
    try:
        from gui.dialog.custom_plot_settings import PlotSettingsManager, LineManager
        from src.features import LineDrawingManager, AnnotationManager
        
        # Test PlotSettingsManager
        settings_manager = PlotSettingsManager()
        print("✓ PlotSettingsManager instantiated successfully")
        
        # Test LineDrawingManager
        line_drawing_manager = LineDrawingManager()
        print("✓ LineDrawingManager instantiated successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Class instantiation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_method_existence():
    """Test if required methods exist"""
    print("\nTesting method existence...")
    
    try:
        from gui.dialog.custom_plot_settings import CustomNavigationToolbar
        
        # Check if the missing methods are now present
        required_methods = [
            'show_line_context_menu',
            'show_line_style_dialog', 
            'show_annotation_context_menu'
        ]
        
        for method_name in required_methods:
            if hasattr(CustomNavigationToolbar, method_name):
                print(f"✓ {method_name} method exists")
            else:
                print(f"✗ {method_name} method missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Method existence check failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("Testing Custom Plot Settings Line Tool Integration...")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_class_instantiation,
        test_method_existence
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✓ All tests passed! Line tool integration should work correctly.")
        print("\nThe following issues have been resolved:")
        print("• Missing show_line_context_menu method")
        print("• Missing show_line_style_dialog method") 
        print("• Missing show_annotation_context_menu method")
        print("• Import dependencies are working")
        print("• Class instantiation is working")
    else:
        print("✗ Some tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    run_all_tests()
