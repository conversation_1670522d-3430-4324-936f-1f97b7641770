#!/usr/bin/env python3
"""
Test script to reproduce the exact issue described by the user
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.features.line_drawing_manager import LineDrawingManager
from src.features.draggable_line import DraggableLine
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


class MockLabel:
    def __init__(self):
        self.text = ""
    def setText(self, text):
        self.text = text

class MockUI:
    def __init__(self):
        self.lblLogInfo = MockLabel()

class MockMainWindow:
    def __init__(self):
        self.current_canvas = None
        self.line_drawing_manager = None
        self.ui = MockUI()


def test_exact_user_scenario():
    """Test the exact scenario described by the user"""
    print("Testing exact user scenario...")
    print("=" * 60)
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    print("Step 1: Create line with default color orange, solid line, thickness 3")
    
    # Set initial defaults
    initial_defaults = {
        'color': 'orange',
        'linewidth': 3,
        'linestyle': '-',
        'alpha': 1.0,
        'arrow_style': 'none'
    }
    manager.update_default_properties(**initial_defaults)
    
    # Create first line
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (2, 2))
    line1 = manager.finish_drawing((8, 8))
    
    print(f"Line1 created with: {line1.get_properties()}")
    
    print("\nStep 2: Right-click on line and change style to dashed, red, thickness 1")
    
    # Simulate right-click property change
    new_style = {
        'color': 'red',
        'linewidth': 1,
        'linestyle': '--',
        'alpha': 1.0
    }
    
    print(f"Changing line1 properties to: {new_style}")
    line1.update_properties(**new_style)
    
    # Verify the change
    line1_after_change = line1.get_properties()
    print(f"Line1 after change: {line1_after_change}")
    
    # Store line1's properties for comparison
    line1_properties_before_new_line = line1.get_properties().copy()
    
    print("\nStep 3: Create another line very near the previous line")
    
    # Create second line very close to the first one
    close_start = (2.1, 2.1)  # Very close to line1's start
    close_end = (8.1, 8.1)    # Very close to line1's end
    
    print(f"Creating line2 from {close_start} to {close_end}")
    
    # Check if the close point is detected as being on line1
    detected_line = manager.find_line_at_point(close_start)
    print(f"Point {close_start} detected as being on line: {detected_line.line_id if detected_line else 'None'}")
    
    # Create the second line
    manager.start_drawing(ax, close_start)
    line2 = manager.finish_drawing(close_end)
    
    print(f"Line2 created with: {line2.get_properties()}")
    
    # Check line1's properties after creating line2
    line1_properties_after_new_line = line1.get_properties().copy()
    
    print("\nStep 4: Check if line1's properties changed")
    
    # Compare line1's properties before and after creating line2
    properties_changed = []
    for key in ['color', 'linewidth', 'linestyle', 'alpha']:
        before = line1_properties_before_new_line[key]
        after = line1_properties_after_new_line[key]
        if before != after:
            properties_changed.append(f"{key}: {before} -> {after}")
    
    if properties_changed:
        print("❌ BUG DETECTED: Line1's properties changed when creating line2!")
        for change in properties_changed:
            print(f"  - {change}")
        return False
    else:
        print("✅ Line1's properties remained unchanged")
    
    print("\nStep 5: Check if line2 uses the correct defaults")
    
    # Line2 should use the updated defaults (red, dashed, thickness 1)
    expected_line2_props = new_style
    line2_issues = []
    
    for key, expected_value in expected_line2_props.items():
        actual_value = line2.properties[key]
        if actual_value != expected_value:
            line2_issues.append(f"{key}: expected {expected_value}, got {actual_value}")
    
    if line2_issues:
        print("❌ Line2 does not use the correct defaults!")
        for issue in line2_issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ Line2 correctly uses the updated defaults")
    
    return True


def test_with_debug_output():
    """Run the test with debug output to see what's happening"""
    print("\n" + "=" * 60)
    print("RUNNING TEST WITH DEBUG OUTPUT")
    print("=" * 60)
    
    try:
        result = test_exact_user_scenario()
        
        print("\n" + "=" * 60)
        if result:
            print("✅ TEST PASSED: No interference detected")
        else:
            print("❌ TEST FAILED: Line interference issue confirmed")
        print("=" * 60)
        
        return result
        
    except Exception as e:
        print(f"❌ TEST FAILED WITH EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_with_debug_output()
    sys.exit(0 if success else 1)
