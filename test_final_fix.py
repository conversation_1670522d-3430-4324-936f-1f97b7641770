#!/usr/bin/env python3
"""
Final test to verify the line interference issue is completely resolved
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.features.line_drawing_manager import LineDrawingManager
from src.features.draggable_line import DraggableLine
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


class MockLabel:
    def __init__(self):
        self.text = ""
    def setText(self, text):
        self.text = text

class MockUI:
    def __init__(self):
        self.lblLogInfo = MockLabel()

class MockMainWindow:
    def __init__(self):
        self.current_canvas = None
        self.line_drawing_manager = None
        self.ui = MockUI()


def test_no_interference_with_fixes():
    """Test that the fixes prevent line interference"""
    print("Testing line interference prevention with fixes...")
    print("=" * 60)
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    print("Step 1: Create first line with orange, solid, thickness 3")
    
    # Set initial defaults
    initial_defaults = {
        'color': 'orange',
        'linewidth': 3,
        'linestyle': '-',
        'alpha': 1.0,
        'arrow_style': 'none'
    }
    manager.update_default_properties(**initial_defaults)
    
    # Create first line
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (1, 1))
    line1 = manager.finish_drawing((5, 5))
    
    print(f"Line1 created: {line1.get_properties()}")
    
    print("\nStep 2: Change line1 to red, dashed, thickness 1")
    
    # Change line1's properties
    new_style = {
        'color': 'red',
        'linewidth': 1,
        'linestyle': '--',
        'alpha': 1.0
    }
    
    line1.update_properties(**new_style)
    line1_after_change = line1.get_properties()
    print(f"Line1 after change: {line1_after_change}")
    
    print("\nStep 3: Create line2 VERY close to line1")
    
    # Store line1's properties before creating line2
    line1_props_before = line1.get_properties().copy()
    
    # Create line2 extremely close to line1
    very_close_start = (1.01, 1.01)  # Extremely close
    very_close_end = (5.01, 5.01)
    
    print(f"Creating line2 from {very_close_start} to {very_close_end}")
    
    # Check detection with normal tolerance
    detected_normal = manager.find_line_at_point(very_close_start)
    print(f"Normal tolerance detection: {detected_normal.line_id if detected_normal else 'None'}")
    
    # Check detection with strict mode
    detected_strict = manager.find_line_at_point(very_close_start, strict_mode=True)
    print(f"Strict mode detection: {detected_strict.line_id if detected_strict else 'None'}")
    
    # Create line2
    manager.start_drawing(ax, very_close_start)
    line2 = manager.finish_drawing(very_close_end)
    
    print(f"Line2 created: {line2.get_properties()}")
    
    # Check line1's properties after creating line2
    line1_props_after = line1.get_properties().copy()
    
    print("\nStep 4: Verify no interference")
    
    # Check if line1's properties changed
    interference_detected = False
    for key in ['color', 'linewidth', 'linestyle', 'alpha']:
        before = line1_props_before[key]
        after = line1_props_after[key]
        if before != after:
            print(f"❌ INTERFERENCE: Line1 {key} changed from {before} to {after}")
            interference_detected = True
    
    if not interference_detected:
        print("✅ No interference detected - line1 properties unchanged")
    
    # Check if line2 uses correct defaults
    line2_correct = True
    for key, expected in new_style.items():
        actual = line2.properties[key]
        if actual != expected:
            print(f"❌ Line2 {key}: expected {expected}, got {actual}")
            line2_correct = False
    
    if line2_correct:
        print("✅ Line2 uses correct defaults")
    
    return not interference_detected and line2_correct


def test_tolerance_improvements():
    """Test that tolerance improvements work correctly"""
    print("\nTesting tolerance improvements...")
    print("=" * 40)
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    # Create a line
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (2, 2))
    line1 = manager.finish_drawing((8, 8))
    
    # Test different distances
    test_points = [
        (2.01, 2.01, "Very close"),
        (2.1, 2.1, "Close"),
        (2.5, 2.5, "Medium distance"),
        (3.0, 3.0, "Far")
    ]
    
    print("Testing point detection with different tolerances:")
    for x, y, description in test_points:
        normal_detected = manager.find_line_at_point((x, y)) is not None
        strict_detected = manager.find_line_at_point((x, y), strict_mode=True) is not None
        
        print(f"  {description} ({x}, {y}): Normal={normal_detected}, Strict={strict_detected}")
    
    return True


def run_final_tests():
    """Run all final tests"""
    print("Running Final Line Interference Fix Tests...")
    print("=" * 70)
    
    try:
        test1_result = test_no_interference_with_fixes()
        test2_result = test_tolerance_improvements()
        
        print("\n" + "=" * 70)
        if test1_result and test2_result:
            print("✅ ALL TESTS PASSED!")
            print("\nFixes implemented:")
            print("• Improved tolerance settings for more precise line detection")
            print("• Added strict mode for double-click line creation")
            print("• Prevented auto-save during line creation")
            print("• Added debug logging to track property changes")
            print("• Removed duplicate contains_point methods")
            print("\nThe line interference issue should now be resolved!")
        else:
            print("❌ SOME TESTS FAILED!")
            print("The line interference issue may still exist.")
        
        return test1_result and test2_result
        
    except Exception as e:
        print(f"❌ TESTS FAILED WITH EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_final_tests()
    sys.exit(0 if success else 1)
