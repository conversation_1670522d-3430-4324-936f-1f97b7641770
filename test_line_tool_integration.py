#!/usr/bin/env python3
"""
Test script to verify the line tool integration in custom plot settings
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import matplotlib
matplotlib.use('Qt5Agg')  # Use Qt backend

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer
import numpy as np

def test_toolbar_creation():
    """Test creating the custom navigation toolbar with line tools"""
    print("Testing CustomNavigationToolbar creation...")
    
    try:
        from gui.dialog.custom_plot_settings import CustomNavigationToolbar
        
        # Create a simple plot
        fig = Figure(figsize=(8, 6))
        ax = fig.add_subplot(111)
        
        # Add some sample data
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y, label='sin(x)')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_title('Test Plot')
        ax.legend()
        
        # Create canvas
        canvas = FigureCanvas(fig)
        
        # Create mock parent
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        parent = QMainWindow()
        
        # Create sample data frame
        data_frame = {
            'data_frame_1': {
                'sin(x)': y
            }
        }
        
        # Create the toolbar
        toolbar = CustomNavigationToolbar(
            canvas=canvas,
            parent=parent,
            data_frame=data_frame
        )
        
        print("✓ CustomNavigationToolbar created successfully")
        
        # Test if line managers are initialized
        if hasattr(toolbar, 'line_manager'):
            print("✓ LineManager initialized")
        else:
            print("⚠ LineManager not initialized (this might be expected)")
            
        if hasattr(toolbar, 'line_drawing_manager'):
            print("✓ LineDrawingManager initialized")
        else:
            print("✗ LineDrawingManager not initialized")
            return False
            
        if hasattr(toolbar, 'annotation_manager'):
            print("✓ AnnotationManager initialized")
        else:
            print("✗ AnnotationManager not initialized")
            return False
        
        # Test if required methods exist and are callable
        required_methods = [
            'show_line_context_menu',
            'show_line_style_dialog',
            'show_annotation_context_menu',
            'toggle_line_drawing_mode',
            'toggle_annotation_mode'
        ]
        
        for method_name in required_methods:
            if hasattr(toolbar, method_name) and callable(getattr(toolbar, method_name)):
                print(f"✓ {method_name} method is callable")
            else:
                print(f"✗ {method_name} method is not callable")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Toolbar creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_line_drawing_functionality():
    """Test the line drawing functionality"""
    print("\nTesting line drawing functionality...")
    
    try:
        from src.features.line_drawing_manager import LineDrawingManager
        
        # Create a mock main window
        class MockMainWindow:
            def __init__(self):
                self.current_canvas = None
                self.ui = MockUI()
        
        class MockUI:
            def __init__(self):
                self.lblLogInfo = MockLabel()
        
        class MockLabel:
            def __init__(self):
                self.text = ""
            def setText(self, text):
                self.text = text
                print(f"Status: {text}")
        
        main_window = MockMainWindow()
        
        # Create line drawing manager
        line_manager = LineDrawingManager(main_window)
        
        print("✓ LineDrawingManager created successfully")
        
        # Test setting drawing mode
        line_manager.set_drawing_mode(True)
        print("✓ Drawing mode enabled")
        
        # Test orientation settings
        line_manager.set_line_orientation('horizontal')
        print("✓ Orientation set to horizontal")
        
        line_manager.set_line_orientation('vertical')
        print("✓ Orientation set to vertical")
        
        line_manager.set_line_orientation('free')
        print("✓ Orientation set to free")
        
        # Test line type settings
        line_manager.set_line_type('arrow')
        print("✓ Line type set to arrow")
        
        line_manager.set_line_type('line')
        print("✓ Line type set to line")
        
        return True
        
    except Exception as e:
        print(f"✗ Line drawing functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all integration tests"""
    print("Testing Line Tool Integration in Custom Plot Settings...")
    print("=" * 65)
    
    tests = [
        test_toolbar_creation,
        test_line_drawing_functionality
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 65)
    if all_passed:
        print("✓ All integration tests passed!")
        print("\nLine tool features are now properly integrated into custom_plot_settings.py:")
        print("• CustomNavigationToolbar with line and annotation tools")
        print("• Missing methods have been implemented")
        print("• Line drawing manager integration")
        print("• Annotation manager integration")
        print("• Context menus for lines and annotations")
        print("• Style dialogs for line customization")
        print("\nThe line tool functionality should now work as intended!")
    else:
        print("✗ Some integration tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    run_all_tests()
