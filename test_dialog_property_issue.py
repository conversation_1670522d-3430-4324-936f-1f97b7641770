#!/usr/bin/env python3
"""
Test script to reproduce the dialog property reset issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.features.line_drawing_manager import LineDrawingManager
from src.features.draggable_line import DraggableLine
from gui.dialog.line_settings_dialog import LineSettingsDialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


class MockLabel:
    def __init__(self):
        self.text = ""
    def setText(self, text):
        self.text = text

class MockUI:
    def __init__(self):
        self.lblLogInfo = MockLabel()

class MockMainWindow:
    def __init__(self):
        self.current_canvas = None
        self.line_drawing_manager = None
        self.ui = MockUI()


def test_dialog_property_persistence():
    """Test the dialog property persistence issue"""
    print("Testing dialog property persistence issue...")
    print("=" * 60)
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    print("Step 1: Create a line with default properties")
    
    # Create a line
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (1, 1))
    line1 = manager.finish_drawing((5, 5))
    
    initial_props = line1.get_properties()
    print(f"Initial line properties: {initial_props}")
    
    print("\nStep 2: Simulate opening settings dialog and changing properties")
    
    # Create settings dialog (simulating opening it)
    dialog = LineSettingsDialog(parent=None, line_manager=manager, current_line=line1)
    
    # Check what the dialog loaded
    loaded_color = dialog.color_edit.text()
    loaded_width = dialog.width_spin.value()
    loaded_style = dialog.style_combo.currentIndex()
    
    print(f"Dialog loaded - Color: {loaded_color}, Width: {loaded_width}, Style: {loaded_style}")
    
    # Simulate user changing settings in dialog
    new_color = "#00FF00"  # Green
    new_width = 4.0
    new_style_index = 1  # Dashed
    
    dialog.color_edit.setText(new_color)
    dialog.width_spin.setValue(new_width)
    dialog.style_combo.setCurrentIndex(new_style_index)
    
    print(f"User changed - Color: {new_color}, Width: {new_width}, Style: dashed")
    
    # Apply settings
    dialog.apply_settings()
    
    # Check line properties after applying
    props_after_dialog = line1.get_properties()
    print(f"Line properties after dialog: {props_after_dialog}")
    
    # Verify the changes were applied
    assert props_after_dialog['color'] == new_color, f"Color not applied: {props_after_dialog['color']} != {new_color}"
    assert props_after_dialog['linewidth'] == new_width, f"Width not applied: {props_after_dialog['linewidth']} != {new_width}"
    assert props_after_dialog['linestyle'] == '--', f"Style not applied: {props_after_dialog['linestyle']} != '--'"
    
    print("✅ Settings applied correctly")
    
    print("\nStep 3: Simulate reopening the dialog")
    
    # Create a new dialog instance (simulating reopening)
    dialog2 = LineSettingsDialog(parent=None, line_manager=manager, current_line=line1)
    
    # Check what the second dialog loaded
    reloaded_color = dialog2.color_edit.text()
    reloaded_width = dialog2.width_spin.value()
    reloaded_style = dialog2.style_combo.currentIndex()
    
    print(f"Dialog2 loaded - Color: {reloaded_color}, Width: {reloaded_width}, Style: {reloaded_style}")
    
    # Check if the dialog loaded the current line properties correctly
    current_props = line1.get_properties()
    
    if reloaded_color != current_props['color']:
        print(f"❌ BUG: Dialog color {reloaded_color} != line color {current_props['color']}")
        return False
    
    if reloaded_width != current_props['linewidth']:
        print(f"❌ BUG: Dialog width {reloaded_width} != line width {current_props['linewidth']}")
        return False
    
    expected_style_index = {'--': 1, ':': 2, '-.': 3}.get(current_props['linestyle'], 0)
    if reloaded_style != expected_style_index:
        print(f"❌ BUG: Dialog style {reloaded_style} != expected {expected_style_index}")
        return False
    
    print("✅ Dialog correctly loaded current line properties")
    
    return True


def test_dragging_property_reset():
    """Test if dragging resets line properties"""
    print("\nTesting dragging property reset issue...")
    print("=" * 40)
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    # Create a line
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (2, 2))
    line1 = manager.finish_drawing((6, 6))
    
    # Change line properties
    custom_props = {
        'color': 'blue',
        'linewidth': 3,
        'linestyle': ':',
        'alpha': 0.8
    }
    
    line1.update_properties(**custom_props)
    props_before_drag = line1.get_properties()
    print(f"Properties before drag: {props_before_drag}")
    
    # Simulate dragging by calling the motion handling methods
    print("Simulating line drag...")
    
    # Simulate mouse press (start drag)
    class MockEvent:
        def __init__(self, x, y):
            self.xdata = x
            self.ydata = y
            self.x = x * 100  # Mock pixel coordinates
            self.y = y * 100
    
    # Start dragging
    line1.press = ((2, 2), 200, 200)
    line1.dragging_point = 'line'
    line1.connected = True
    
    # Simulate motion
    motion_event = MockEvent(2.5, 2.5)
    line1.handle_motion(motion_event)
    
    # Check properties after drag
    props_after_drag = line1.get_properties()
    print(f"Properties after drag: {props_after_drag}")
    
    # Check if any properties changed during dragging
    properties_changed = []
    for key in custom_props.keys():
        if props_before_drag[key] != props_after_drag[key]:
            properties_changed.append(f"{key}: {props_before_drag[key]} -> {props_after_drag[key]}")
    
    if properties_changed:
        print("❌ BUG: Properties changed during dragging!")
        for change in properties_changed:
            print(f"  - {change}")
        return False
    else:
        print("✅ Properties remained unchanged during dragging")
        return True


def run_all_tests():
    """Run all dialog property tests"""
    print("Running Dialog Property Issue Tests...")
    print("=" * 70)
    
    try:
        test1_result = test_dialog_property_persistence()
        test2_result = test_dragging_property_reset()
        
        print("\n" + "=" * 70)
        if test1_result and test2_result:
            print("✅ ALL TESTS PASSED!")
            print("No dialog property issues detected.")
        else:
            print("❌ DIALOG PROPERTY ISSUES DETECTED!")
            if not test1_result:
                print("• Dialog not loading current line properties correctly")
            if not test2_result:
                print("• Dragging resets line properties")
        
        return test1_result and test2_result
        
    except Exception as e:
        print(f"❌ TESTS FAILED WITH EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
