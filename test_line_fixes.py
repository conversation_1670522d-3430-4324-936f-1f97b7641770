#!/usr/bin/env python3
"""
Test script to verify the line tool fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.features.line_drawing_manager import LineDrawingManager
from src.features.draggable_line import DraggableLine
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


def test_arrow_style_none():
    """Test that 'none' arrow style works correctly"""
    print("Testing 'none' arrow style...")
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create a line with 'none' arrow style
    line = DraggableLine(
        start_point=(1, 1),
        end_point=(5, 5),
        canvas=canvas,
        line_type='arrow'  # This is arrow type but with 'none' style
    )
    
    # Update properties to use 'none' arrow style
    line.update_properties(arrow_style='none')
    
    # Verify it created a Line2D instead of FancyArrowPatch
    from matplotlib.lines import Line2D
    assert isinstance(line.artist, Line2D), f"Expected Line2D, got {type(line.artist)}"
    
    print("✓ 'none' arrow style test passed!")


def test_artist_removal():
    """Test that artist removal doesn't cause errors"""
    print("Testing artist removal...")
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create a line
    line = DraggableLine(
        start_point=(1, 1),
        end_point=(5, 5),
        canvas=canvas,
        line_type='line'
    )
    
    # Try to remove it multiple times (should not cause errors)
    line.remove()
    line.remove()  # Second removal should not cause error
    
    print("✓ Artist removal test passed!")


def test_line_manager_defaults():
    """Test that line manager has correct defaults"""
    print("Testing line manager defaults...")
    
    manager = LineDrawingManager()
    defaults = manager.get_default_properties()
    
    # Check that default line type is 'line' not 'arrow'
    assert manager.current_line_type == 'line', f"Expected 'line', got {manager.current_line_type}"
    
    # Check that default arrow style is 'none'
    assert defaults['arrow_style'] == 'none', f"Expected 'none', got {defaults['arrow_style']}"
    
    print("✓ Line manager defaults test passed!")


def test_orientation_constraints():
    """Test orientation constraint functionality"""
    print("Testing orientation constraints...")
    
    manager = LineDrawingManager()
    
    # Test orientation setting
    manager.set_line_orientation('horizontal')
    assert manager.get_line_orientation() == 'horizontal'
    
    # Test constraint application
    constrained = manager._apply_orientation_constraint((1, 1), (5, 3))
    assert constrained == (5, 1), f"Expected (5, 1), got {constrained}"
    
    manager.set_line_orientation('vertical')
    constrained = manager._apply_orientation_constraint((1, 1), (5, 3))
    assert constrained == (1, 3), f"Expected (1, 3), got {constrained}"
    
    print("✓ Orientation constraints test passed!")


def run_fix_tests():
    """Run all fix tests"""
    print("Running Line Tool Fix Tests...")
    print("=" * 50)
    
    try:
        test_arrow_style_none()
        test_artist_removal()
        test_line_manager_defaults()
        test_orientation_constraints()
        
        print("=" * 50)
        print("✓ All fix tests passed successfully!")
        print("\nFixed Issues:")
        print("• 'none' arrow style now works correctly")
        print("• Artist removal errors are handled gracefully")
        print("• Import errors for QAction fixed")
        print("• Default line type is now 'line' instead of 'arrow'")
        print("• Orientation constraints work properly")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_fix_tests()
    sys.exit(0 if success else 1)
