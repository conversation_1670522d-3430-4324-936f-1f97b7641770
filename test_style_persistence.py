#!/usr/bin/env python3
"""
Test script to verify style persistence functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.features.line_drawing_manager import LineDrawingManager
from src.features.draggable_line import DraggableLine
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


def test_style_persistence():
    """Test that line style changes are automatically saved as defaults"""
    print("Testing style persistence...")
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create line manager
    manager = LineDrawingManager()
    
    # Check initial defaults
    initial_defaults = manager.get_default_properties()
    print(f"Initial defaults: {initial_defaults}")
    
    # Create first line
    line1 = DraggableLine(
        start_point=(1, 1),
        end_point=(5, 5),
        canvas=canvas,
        main_window=manager,  # Use manager as mock main window
        line_type='line'
    )
    
    # Apply initial defaults to line1
    line1.properties.update(initial_defaults)
    
    # Change line1's properties
    new_props = {
        'color': 'blue',
        'linewidth': 3,
        'linestyle': '--',
        'alpha': 0.8
    }
    
    print(f"Updating line1 with: {new_props}")
    line1.update_properties(**new_props)
    
    # Check that defaults were updated
    updated_defaults = manager.get_default_properties()
    print(f"Updated defaults: {updated_defaults}")
    
    # Verify that the new properties are now defaults
    for key, value in new_props.items():
        assert updated_defaults[key] == value, f"Expected {key}={value}, got {updated_defaults[key]}"
    
    # Create second line and verify it uses the new defaults
    line2 = DraggableLine(
        start_point=(2, 2),
        end_point=(6, 6),
        canvas=canvas,
        main_window=manager,
        line_type='line'
    )
    
    # Apply defaults to line2
    line2.properties.update(manager.get_default_properties())
    
    # Verify line2 has the same properties as line1
    for key, value in new_props.items():
        assert line2.properties[key] == value, f"Line2 {key}={line2.properties[key]}, expected {value}"
    
    print("✓ Style persistence test passed!")


def test_line_settings_dialog_persistence():
    """Test that line settings dialog saves as defaults"""
    print("Testing line settings dialog persistence...")
    
    # Create line manager
    manager = LineDrawingManager()
    
    # Simulate dialog settings
    dialog_settings = {
        'color': '#00FF00',
        'linewidth': 4.0,
        'linestyle': ':',
        'alpha': 0.7,
        'arrow_style': '->',
        'arrow_size': 20,
        'auto_apply': True  # This should trigger saving as defaults
    }
    
    # Simulate applying settings with auto_apply enabled
    line_props = {k: v for k, v in dialog_settings.items() 
                 if k in ['color', 'linewidth', 'linestyle', 'alpha', 'arrow_style', 'arrow_size']}
    
    if dialog_settings.get('auto_apply', True):
        manager.update_default_properties(**line_props)
    
    # Verify defaults were updated
    defaults = manager.get_default_properties()
    for key, value in line_props.items():
        assert defaults[key] == value, f"Expected {key}={value}, got {defaults[key]}"
    
    print("✓ Line settings dialog persistence test passed!")


def test_no_duplicate_settings():
    """Test that there's only one line settings system"""
    print("Testing for duplicate settings systems...")
    
    # Try to import the old line style dialog (should fail)
    try:
        from gui.dialog.line_style_dialog import LineStyleDialog
        assert False, "Old LineStyleDialog should have been removed"
    except ImportError:
        print("✓ Old LineStyleDialog successfully removed")
    
    # Try to import the old LineSettingsDialog from custom_plot_settings (should fail)
    try:
        from gui.dialog.custom_plot_settings import LineSettingsDialog
        assert False, "Old LineSettingsDialog should have been removed from custom_plot_settings"
    except ImportError:
        print("✓ Old LineSettingsDialog successfully removed from custom_plot_settings")
    
    # Verify the new dialog exists
    try:
        from gui.dialog.line_settings_dialog import LineSettingsDialog
        print("✓ New comprehensive LineSettingsDialog is available")
    except ImportError:
        assert False, "New LineSettingsDialog should be available"
    
    print("✓ No duplicate settings systems test passed!")


def run_all_tests():
    """Run all style persistence tests"""
    print("Running Style Persistence Tests...")
    print("=" * 50)
    
    try:
        test_style_persistence()
        test_line_settings_dialog_persistence()
        test_no_duplicate_settings()
        
        print("=" * 50)
        print("✓ All style persistence tests passed successfully!")
        print("\nStyle Persistence Features Verified:")
        print("• Line property changes automatically save as defaults")
        print("• Line settings dialog auto-apply functionality works")
        print("• Duplicate settings systems have been removed")
        print("• New lines automatically use the last applied settings")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
