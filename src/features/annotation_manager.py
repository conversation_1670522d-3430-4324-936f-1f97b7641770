"""
Annotation Manager for User-Defined Annotations
Handles creation, editing, and management of custom plot annotations
"""

import uuid
from typing import Dict, List, Optional, Tuple
from matplotlib.text import Annotation
from .draggable_annotation import DraggableAnnotation


class AnnotationManager:
    """
    Manages user-defined annotations on plots.
    Provides functionality to add, edit, delete, and style custom annotations.
    """
    
    def __init__(self, main_window=None):
        self.main_window = main_window
        self.user_annotations: Dict[str, DraggableAnnotation] = {}
        self.annotation_mode = False  # Toggle for annotation creation mode
        self.default_style = {
            'fontsize': 10,
            'color': 'black',
            'bbox': dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
            'ha': 'center',
            'va': 'center'
        }
    
    def set_annotation_mode(self, enabled: bool):
        """Enable or disable annotation creation mode"""
        self.annotation_mode = enabled
        if self.main_window:
            if enabled:
                self.main_window.ui.lblLogInfo.setText("Annotation mode enabled - double-click to add annotations")
            else:
                self.main_window.ui.lblLogInfo.setText("Annotation mode disabled")
    
    def is_annotation_mode_enabled(self) -> bool:
        """Check if annotation creation mode is enabled"""
        return self.annotation_mode
    
    def create_annotation(self, axes, x: float, y: float, text: str = "New Annotation") -> Optional[DraggableAnnotation]:
        """
        Create a new user annotation at the specified coordinates
        
        Args:
            axes: The matplotlib axes to add the annotation to
            x: X coordinate in data space
            y: Y coordinate in data space
            text: Initial text for the annotation
            
        Returns:
            DraggableAnnotation object or None if creation failed
        """
        try:
            # Generate unique key for this annotation
            annotation_key = f"user_annotation_{uuid.uuid4().hex[:8]}"
            
            # Create matplotlib annotation
            annotation = axes.annotate(
                text,
                xy=(x, y),
                xytext=(x, y),
                **self.default_style
            )
            
            # Create draggable wrapper
            if self.main_window and hasattr(self.main_window, 'current_canvas'):
                draggable_annotation = DraggableAnnotation(
                    annotation=annotation,
                    canvas=self.main_window.current_canvas,
                    main_window=self.main_window,
                    position_key=annotation_key,
                    annotation_type='user'
                )
                
                # Store the annotation
                self.user_annotations[annotation_key] = draggable_annotation

                # Enable dragging by default for user annotations
                draggable_annotation.connect()

                print(f"[DEBUG] Created user annotation: {annotation_key}")
                return draggable_annotation
            
        except Exception as e:
            print(f"[ERROR] Failed to create annotation: {e}")
            return None
    
    def edit_annotation(self, draggable_annotation: DraggableAnnotation):
        """Open edit dialog for an annotation"""
        if self.main_window and hasattr(self.main_window, 'show_annotation_edit_dialog'):
            self.main_window.show_annotation_edit_dialog(draggable_annotation)
    
    def delete_annotation(self, draggable_annotation: DraggableAnnotation):
        """Delete an annotation"""
        try:
            # Remove from matplotlib
            draggable_annotation.annotation.remove()
            
            # Disconnect from event system
            draggable_annotation.disconnect()
            
            # Remove from our tracking
            if draggable_annotation.position_key in self.user_annotations:
                del self.user_annotations[draggable_annotation.position_key]
            
            # Remove from main window's annotation positions
            if (self.main_window and 
                hasattr(self.main_window, 'annotation_positions') and
                draggable_annotation.position_key in self.main_window.annotation_positions):
                del self.main_window.annotation_positions[draggable_annotation.position_key]
            
            # Redraw canvas
            if self.main_window and hasattr(self.main_window, 'current_canvas'):
                self.main_window.current_canvas.draw_idle()
            
            print(f"[DEBUG] Deleted annotation: {draggable_annotation.position_key}")
            
        except Exception as e:
            print(f"[ERROR] Failed to delete annotation: {e}")
    
    def clear_all_user_annotations(self):
        """Clear all user-defined annotations"""
        for annotation_key in list(self.user_annotations.keys()):
            self.delete_annotation(self.user_annotations[annotation_key])
        self.user_annotations.clear()
    
    def get_annotation_count(self) -> int:
        """Get the number of user annotations"""
        return len(self.user_annotations)
    
    def export_annotations(self) -> List[Dict]:
        """Export annotation data for saving/loading"""
        annotations_data = []
        for key, draggable_annotation in self.user_annotations.items():
            try:
                if draggable_annotation.use_xytext:
                    position = draggable_annotation.annotation.xytext
                else:
                    position = draggable_annotation.annotation.get_position()
                
                annotation_data = {
                    'key': key,
                    'text': draggable_annotation.annotation.get_text(),
                    'position': position,
                    'use_xytext': draggable_annotation.use_xytext
                }
                annotations_data.append(annotation_data)
            except Exception as e:
                print(f"[ERROR] Failed to export annotation {key}: {e}")
        
        return annotations_data
    
    def import_annotations(self, axes, annotations_data: List[Dict]):
        """Import annotation data from saved state"""
        for annotation_data in annotations_data:
            try:
                # Create matplotlib annotation
                annotation = axes.annotate(
                    annotation_data['text'],
                    xy=annotation_data['position'],
                    xytext=annotation_data['position'] if annotation_data.get('use_xytext') else None,
                    **self.default_style
                )
                
                # Create draggable wrapper
                if self.main_window and hasattr(self.main_window, 'current_canvas'):
                    draggable_annotation = DraggableAnnotation(
                        annotation=annotation,
                        canvas=self.main_window.current_canvas,
                        main_window=self.main_window,
                        position_key=annotation_data['key'],
                        annotation_type='user'
                    )
                    
                    # Store the annotation
                    self.user_annotations[annotation_data['key']] = draggable_annotation
                    
                    print(f"[DEBUG] Imported user annotation: {annotation_data['key']}")
                    
            except Exception as e:
                print(f"[ERROR] Failed to import annotation: {e}")
