"""
Line Drawing Manager
Handles creation, editing, and management of draggable lines and arrows
"""

from typing import Dict, List, Optional, Tuple
from .draggable_line import DraggableLine


class LineDrawingManager:
    """
    Manages line and arrow drawing functionality on plots.
    Handles creation, editing, deletion, and styling of lines/arrows.
    """
    
    def __init__(self, main_window=None):
        self.main_window = main_window
        self.lines: Dict[str, DraggableLine] = {}
        self.drawing_mode = False
        self.current_line_type = 'line'  # 'line' or 'arrow' - default to line
        self.drawing_start_point = None
        self.preview_line = None
        self.line_orientation = 'free'  # 'free', 'horizontal', 'vertical'
        self.selected_lines: List[DraggableLine] = []
        self.undo_stack: List[Dict] = []
        self.redo_stack: List[Dict] = []
        self.snap_to_grid = False
        self.grid_size = 1.0  # Grid spacing for snapping
        self._creating_line = False  # Flag to prevent interference during line creation

        # Default properties for new lines (persistent style memory)
        self.default_properties = {
            'color': 'red',
            'linewidth': 2,
            'linestyle': '-',
            'alpha': 1.0,
            'arrow_style': 'none',  # Default to no arrow
            'arrow_size': 15,
            'connection_style': 'arc3,rad=0'
        }

        # Load saved preferences
        self._load_style_preferences()
    
    def set_drawing_mode(self, enabled: bool):
        """Enable or disable line drawing mode"""
        self.drawing_mode = enabled
        if self.main_window and hasattr(self.main_window.ui, 'lblLogInfo'):
            if enabled:
                orientation = self.line_orientation.title()
                self.main_window.ui.lblLogInfo.setText(
                    f"Line Tool Active - Mode: {orientation} | "
                    f"Double-click-drag to create, single-click to select/move"
                )
            else:
                self.main_window.ui.lblLogInfo.setText("Line Tool Disabled")
                # Cancel any ongoing drawing
                self.cancel_drawing()
    
    def set_line_type(self, line_type: str):
        """Set the type of line to draw ('line' or 'arrow')"""
        if line_type in ['line', 'arrow']:
            self.current_line_type = line_type
            print(f"[DEBUG] Line type set to: {line_type}")
    
    def is_drawing_mode_enabled(self) -> bool:
        """Check if line drawing mode is enabled"""
        return self.drawing_mode
    
    def start_drawing(self, axes, start_point: Tuple[float, float]):
        """Start drawing a line from the given point"""
        if not self.drawing_mode:
            return False

        self._creating_line = True  # Set flag to prevent interference
        self.drawing_start_point = start_point
        print(f"[DEBUG] Started drawing {self.current_line_type} from {start_point}")

        # Create preview line
        self.create_preview_line(axes, start_point, start_point)
        return True
    
    def update_drawing(self, end_point: Tuple[float, float]):
        """Update the preview line while drawing"""
        if self.drawing_start_point is None or self.preview_line is None:
            return False

        # Apply orientation constraints to preview
        constrained_end_point = self._apply_orientation_constraint(self.drawing_start_point, end_point)

        # Update preview line end point
        self.preview_line.end_point = constrained_end_point
        self.preview_line.update_artist()
        return True
    
    def finish_drawing(self, end_point: Tuple[float, float]) -> Optional[DraggableLine]:
        """Finish drawing and create the final line"""
        if self.drawing_start_point is None:
            return None

        # Apply snap-to-grid
        snapped_start = self.snap_point_to_grid(self.drawing_start_point)
        snapped_end = self.snap_point_to_grid(end_point)

        # Apply orientation constraints
        constrained_end_point = self._apply_orientation_constraint(snapped_start, snapped_end)

        # Remove preview line
        if self.preview_line:
            self.preview_line.remove()
            self.preview_line = None

        # Create final line
        if self.main_window and hasattr(self.main_window, 'current_canvas'):
            line = DraggableLine(
                start_point=snapped_start,
                end_point=constrained_end_point,
                canvas=self.main_window.current_canvas,
                main_window=self.main_window,
                line_type=self.current_line_type,
                initial_properties=self.default_properties  # Pass current defaults
            )

            # Store the line
            self.lines[line.line_id] = line

            # Always enable interaction for new lines
            line.connect()

            print(f"[DEBUG] Created {self.current_line_type}: {line.line_id}")

            # Reset drawing state
            self.drawing_start_point = None
            self._creating_line = False  # Clear flag

            return line

        # Clear flag if we reach here without creating a line
        self._creating_line = False
        return None

    def _apply_orientation_constraint(self, start_point: Tuple[float, float], end_point: Tuple[float, float]) -> Tuple[float, float]:
        """Apply orientation constraints to line endpoints"""
        if self.line_orientation == 'horizontal':
            return (end_point[0], start_point[1])  # Keep Y constant
        elif self.line_orientation == 'vertical':
            return (start_point[0], end_point[1])  # Keep X constant
        else:
            return end_point  # Free orientation
    
    def cancel_drawing(self):
        """Cancel the current drawing operation"""
        if self.preview_line:
            self.preview_line.remove()
            self.preview_line = None
        self.drawing_start_point = None
        self._creating_line = False  # Clear flag
        print("[DEBUG] Drawing cancelled")
    
    def create_preview_line(self, axes, start_point: Tuple[float, float], end_point: Tuple[float, float]):
        """Create a preview line for visual feedback during drawing"""
        if self.main_window and hasattr(self.main_window, 'current_canvas'):
            # Create preview properties based on current defaults
            preview_props = self.default_properties.copy()
            preview_props['alpha'] = 0.5
            preview_props['linestyle'] = '--'

            self.preview_line = DraggableLine(
                start_point=start_point,
                end_point=end_point,
                canvas=self.main_window.current_canvas,
                main_window=None,  # Don't register preview lines
                line_type=self.current_line_type,
                initial_properties=preview_props  # Pass preview properties
            )
    
    def delete_line(self, line: DraggableLine):
        """Delete a line"""
        try:
            # Remove from matplotlib
            line.remove()
            
            # Disconnect from event system
            line.disconnect()
            
            # Remove from our tracking
            if line.line_id in self.lines:
                del self.lines[line.line_id]
            
            print(f"[DEBUG] Deleted line: {line.line_id}")
            
        except Exception as e:
            print(f"[ERROR] Failed to delete line: {e}")
    
    def clear_all_lines(self):
        """Clear all lines"""
        for line_id in list(self.lines.keys()):
            self.delete_line(self.lines[line_id])
        self.lines.clear()
        self.cancel_drawing()
    
    def get_line_count(self) -> int:
        """Get the number of lines"""
        return len(self.lines)
    
    def find_line_at_point(self, point: Tuple[float, float], tolerance=None, strict_mode=False) -> Optional[DraggableLine]:
        """Find a line near the given point"""
        # Use stricter tolerance for line creation to avoid interference
        if strict_mode and tolerance is None:
            # Use a much smaller tolerance for double-click line creation
            tolerance = 0.1  # Very small tolerance to avoid detecting nearby lines

        for line in self.lines.values():
            if line.contains_point(point, tolerance):
                return line
        return None
    
    def update_default_properties(self, **kwargs):
        """Update default properties for new lines and save preferences"""
        self.default_properties.update(kwargs)
        self._save_style_preferences()  # Persist changes
        print(f"[DEBUG] Updated default line properties: {kwargs}")
    
    def get_default_properties(self):
        """Get current default properties"""
        return self.default_properties.copy()

    def _load_style_preferences(self):
        """Load saved style preferences from file"""
        try:
            import json
            import os
            from pathlib import Path

            # Use same path as other plot settings
            base = Path(os.getenv("APPDATA") or (Path.home() / ".local" / "share"))
            prefs_path = base / "plottool" / "line_preferences.json"

            if prefs_path.exists():
                with open(prefs_path, 'r') as f:
                    saved_prefs = json.load(f)
                    self.default_properties.update(saved_prefs)
                    print(f"[DEBUG] Loaded line style preferences: {saved_prefs}")
        except Exception as e:
            print(f"[DEBUG] Could not load line preferences: {e}")

    def _save_style_preferences(self):
        """Save current style preferences to file"""
        try:
            import json
            import os
            from pathlib import Path

            base = Path(os.getenv("APPDATA") or (Path.home() / ".local" / "share"))
            prefs_path = base / "plottool" / "line_preferences.json"

            # Ensure directory exists
            prefs_path.parent.mkdir(parents=True, exist_ok=True)

            with open(prefs_path, 'w') as f:
                json.dump(self.default_properties, f, indent=2)
                print(f"[DEBUG] Saved line style preferences: {self.default_properties}")
        except Exception as e:
            print(f"[DEBUG] Could not save line preferences: {e}")

    def set_line_orientation(self, orientation: str):
        """Set line orientation constraint ('free', 'horizontal', 'vertical')"""
        if orientation in ['free', 'horizontal', 'vertical']:
            self.line_orientation = orientation
            print(f"[DEBUG] Line orientation set to: {orientation}")

            # Update status in main window if available
            if self.main_window and hasattr(self.main_window.ui, 'lblLogInfo') and self.drawing_mode:
                self.main_window.ui.lblLogInfo.setText(
                    f"Line Tool Active - Mode: {orientation.title()} | "
                    f"Double-click-drag to create, single-click to select/move"
                )

    def get_line_orientation(self) -> str:
        """Get current line orientation constraint"""
        return self.line_orientation

    def set_snap_to_grid(self, enabled: bool, grid_size: float = 1.0):
        """Enable or disable snap-to-grid functionality"""
        self.snap_to_grid = enabled
        self.grid_size = grid_size
        print(f"[DEBUG] Snap to grid: {'enabled' if enabled else 'disabled'} (size: {grid_size})")

    def is_snap_to_grid_enabled(self) -> bool:
        """Check if snap-to-grid is enabled"""
        return self.snap_to_grid

    def snap_point_to_grid(self, point: Tuple[float, float]) -> Tuple[float, float]:
        """Snap a point to the grid if snap-to-grid is enabled"""
        if not self.snap_to_grid:
            return point

        x, y = point
        snapped_x = round(x / self.grid_size) * self.grid_size
        snapped_y = round(y / self.grid_size) * self.grid_size
        return (snapped_x, snapped_y)

    def select_line(self, line: DraggableLine, add_to_selection: bool = False):
        """Select a line, optionally adding to current selection"""
        if not add_to_selection:
            self.clear_selection()

        if line not in self.selected_lines:
            self.selected_lines.append(line)
            line.set_selected(True)

    def deselect_line(self, line: DraggableLine):
        """Deselect a specific line"""
        if line in self.selected_lines:
            self.selected_lines.remove(line)
            line.set_selected(False)

    def clear_selection(self):
        """Clear all line selections"""
        for line in self.selected_lines:
            line.set_selected(False)
        self.selected_lines.clear()

    def delete_selected_lines(self):
        """Delete all selected lines"""
        lines_to_delete = self.selected_lines.copy()
        self.clear_selection()

        for line in lines_to_delete:
            self.delete_line(line)

    def get_selected_lines(self) -> List[DraggableLine]:
        """Get list of currently selected lines"""
        return self.selected_lines.copy()

    def show_settings_dialog(self, line: Optional[DraggableLine] = None):
        """Show line settings dialog"""
        try:
            from gui.dialog.line_settings_dialog import LineSettingsDialog

            # Ensure we have the most current line properties
            if line:
                pass  # Debug: print(f"[DEBUG] Showing settings for line {line.line_id} with properties: {line.get_properties()}")

            dialog = LineSettingsDialog(
                parent=self.main_window,
                line_manager=self,
                current_line=line
            )

            # Force refresh the dialog with current line properties
            if line:
                dialog.refresh_from_line()

            def on_settings_changed(settings):
                # Handle settings changes
                if hasattr(self.main_window, 'current_canvas'):
                    self.main_window.current_canvas.draw_idle()

            dialog.settings_changed.connect(on_settings_changed)
            result = dialog.exec()

            # Debug: Check line properties after dialog
            if line:
                pass  # Debug: print(f"[DEBUG] Line properties after dialog: {line.get_properties()}")

            return result

        except Exception as e:
            print(f"[ERROR] Failed to show line settings dialog: {e}")

    def create_line_with_orientation_dialog(self, start_point: Tuple[float, float], end_point: Tuple[float, float]):
        """Create line with orientation selection dialog"""
        try:
            from PySide6.QtWidgets import QMessageBox

            if not self.main_window:
                return None

            # Create orientation selection dialog
            msg_box = QMessageBox(self.main_window)
            msg_box.setWindowTitle("Line Orientation")
            msg_box.setText("Choose line orientation:")

            free_btn = msg_box.addButton("Free", QMessageBox.ActionRole)
            horizontal_btn = msg_box.addButton("Horizontal", QMessageBox.ActionRole)
            vertical_btn = msg_box.addButton("Vertical", QMessageBox.ActionRole)
            cancel_btn = msg_box.addButton(QMessageBox.Cancel)

            msg_box.exec()

            clicked_button = msg_box.clickedButton()

            if clicked_button == cancel_btn:
                return None
            elif clicked_button == horizontal_btn:
                end_point = (end_point[0], start_point[1])
            elif clicked_button == vertical_btn:
                end_point = (start_point[0], end_point[1])
            # Free orientation uses end_point as-is

            # Create the line
            if hasattr(self.main_window, 'current_canvas'):
                line = DraggableLine(
                    start_point=start_point,
                    end_point=end_point,
                    canvas=self.main_window.current_canvas,
                    main_window=self.main_window,
                    line_type=self.current_line_type,
                    initial_properties=self.default_properties  # Pass current defaults
                )

                # Store the line
                self.lines[line.line_id] = line

                # Enable interaction
                line.connect()

                print(f"[DEBUG] Created {self.current_line_type} with orientation: {line.line_id}")
                return line

        except Exception as e:
            print(f"[ERROR] Failed to create line with orientation dialog: {e}")

        return None

    def create_horizontal_line(self, y_position: float, x_start: Optional[float] = None, x_end: Optional[float] = None):
        """Create a horizontal line at specified Y position"""
        if not self.main_window or not hasattr(self.main_window, 'current_canvas'):
            return None

        try:
            # Get current axes limits if start/end not specified
            if hasattr(self.main_window.current_canvas, 'figure') and self.main_window.current_canvas.figure.axes:
                ax = self.main_window.current_canvas.figure.axes[0]
                xlim = ax.get_xlim()

                if x_start is None:
                    x_start = xlim[0]
                if x_end is None:
                    x_end = xlim[1]
            else:
                if x_start is None:
                    x_start = 0
                if x_end is None:
                    x_end = 10

            line = DraggableLine(
                start_point=(x_start, y_position),
                end_point=(x_end, y_position),
                canvas=self.main_window.current_canvas,
                main_window=self.main_window,
                line_type=self.current_line_type,
                initial_properties=self.default_properties  # Pass current defaults
            )

            # Set horizontal constraint
            line.set_orientation_constraint('horizontal')

            # Store and connect the line
            self.lines[line.line_id] = line
            line.connect()

            print(f"[DEBUG] Created horizontal line: {line.line_id}")
            return line

        except Exception as e:
            print(f"[ERROR] Failed to create horizontal line: {e}")
            return None

    def create_vertical_line(self, x_position: float, y_start: Optional[float] = None, y_end: Optional[float] = None):
        """Create a vertical line at specified X position"""
        if not self.main_window or not hasattr(self.main_window, 'current_canvas'):
            return None

        try:
            # Get current axes limits if start/end not specified
            if hasattr(self.main_window.current_canvas, 'figure') and self.main_window.current_canvas.figure.axes:
                ax = self.main_window.current_canvas.figure.axes[0]
                ylim = ax.get_ylim()

                if y_start is None:
                    y_start = ylim[0]
                if y_end is None:
                    y_end = ylim[1]
            else:
                if y_start is None:
                    y_start = 0
                if y_end is None:
                    y_end = 10

            line = DraggableLine(
                start_point=(x_position, y_start),
                end_point=(x_position, y_end),
                canvas=self.main_window.current_canvas,
                main_window=self.main_window,
                line_type=self.current_line_type,
                initial_properties=self.default_properties  # Pass current defaults
            )

            # Set vertical constraint
            line.set_orientation_constraint('vertical')

            # Store and connect the line
            self.lines[line.line_id] = line
            line.connect()

            print(f"[DEBUG] Created vertical line: {line.line_id}")
            return line

        except Exception as e:
            print(f"[ERROR] Failed to create vertical line: {e}")
            return None
    
    def export_lines(self) -> List[Dict]:
        """Export line data for saving/loading"""
        lines_data = []
        for line_id, line in self.lines.items():
            try:
                line_data = {
                    'line_id': line_id,
                    'start_point': line.start_point,
                    'end_point': line.end_point,
                    'line_type': line.line_type,
                    'properties': line.get_properties()
                }
                lines_data.append(line_data)
            except Exception as e:
                print(f"[ERROR] Failed to export line {line_id}: {e}")
        
        return lines_data
    
    def import_lines(self, axes, lines_data: List[Dict]):
        """Import line data from saved state"""
        for line_data in lines_data:
            try:
                if self.main_window and hasattr(self.main_window, 'current_canvas'):
                    line = DraggableLine(
                        start_point=line_data['start_point'],
                        end_point=line_data['end_point'],
                        canvas=self.main_window.current_canvas,
                        main_window=self.main_window,
                        line_type=line_data['line_type']
                    )
                    
                    # Apply saved properties
                    line.update_properties(**line_data['properties'])
                    
                    # Store the line
                    self.lines[line_data['line_id']] = line
                    
                    print(f"[DEBUG] Imported line: {line_data['line_id']}")
                    
            except Exception as e:
                print(f"[ERROR] Failed to import line: {e}")
    
    def enable_line_dragging(self):
        """Enable dragging for all lines"""
        for line in self.lines.values():
            line.connect()
        print(f"[DEBUG] Enabled dragging for {len(self.lines)} lines")
    
    def disable_line_dragging(self):
        """Disable dragging for all lines"""
        for line in self.lines.values():
            line.disconnect()
        print(f"[DEBUG] Disabled dragging for {len(self.lines)} lines")
