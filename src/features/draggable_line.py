"""
Draggable Line/Arrow Implementation
Handles creation and manipulation of draggable lines and arrows on plots
"""

import uuid
from typing import Optional, <PERSON>ple
import numpy as np
from matplotlib.patches import FancyArrowPatch
from matplotlib.lines import Line2D
import matplotlib.patches as mpatches


class DraggableLine:
    """
    A class to create and manage draggable lines and arrows on matplotlib plots.
    Supports various line styles, colors, thickness, and arrow configurations.
    """
    
    def __init__(self, start_point, end_point, canvas, main_window=None, line_type='arrow', initial_properties=None):
        self.start_point = start_point
        self.end_point = end_point
        self.canvas = canvas
        self.main_window = main_window
        self.line_type = line_type  # 'line' or 'arrow'
        self.line_id = f"line_{uuid.uuid4().hex[:8]}"

        # Line properties - use provided properties or defaults
        if initial_properties:
            self.properties = initial_properties.copy()
        else:
            # Fallback defaults if no properties provided
            self.properties = {
                'color': 'red',
                'linewidth': 2,
                'linestyle': '-',  # '-', '--', '-.', ':'
                'alpha': 1.0,
                'arrow_style': 'none',  # '->', '<->', '<-', 'none'
                'arrow_size': 15,
                'connection_style': 'arc3,rad=0'  # For curved arrows
            }
        
        # Interaction state
        self.press = None
        self.dragging_point = None  # 'start', 'end', or 'line'
        self.connected = False
        self.artist = None
        self.selected = False
        self.original_properties = {}  # Store original properties for selection feedback
        self.orientation_constraint = 'free'  # 'free', 'horizontal', 'vertical'
        self._dragging = False  # Flag to prevent property updates during dragging
        
        # Create the visual element
        self.create_artist()

        # Register with main window if available
        if self.main_window and hasattr(self.main_window, 'register_draggable_line'):
            self.main_window.register_draggable_line(self)

        # Connect event handlers by default to make lines draggable
        self.connect()
    
    def create_artist(self):
        """Create the matplotlib artist (line or arrow)"""
        if self.line_type == 'arrow':
            # Handle 'none' arrow style by using line instead
            arrow_style = self.properties['arrow_style']
            if arrow_style == 'none':
                # Create a simple line instead of arrow
                self.artist = Line2D(
                    [self.start_point[0], self.end_point[0]],
                    [self.start_point[1], self.end_point[1]],
                    color=self.properties['color'],
                    linewidth=self.properties['linewidth'],
                    linestyle=self.properties['linestyle'],
                    alpha=self.properties['alpha'],
                    picker=True,
                    pickradius=5
                )
            else:
                self.artist = FancyArrowPatch(
                    self.start_point, self.end_point,
                    arrowstyle=arrow_style,
                    color=self.properties['color'],
                    linewidth=self.properties['linewidth'],
                    linestyle=self.properties['linestyle'],
                    alpha=self.properties['alpha'],
                    connectionstyle=self.properties['connection_style']
                )
                # Set picker after creation
                self.artist.set_picker(True)
        else:  # line
            self.artist = Line2D(
                [self.start_point[0], self.end_point[0]],
                [self.start_point[1], self.end_point[1]],
                color=self.properties['color'],
                linewidth=self.properties['linewidth'],
                linestyle=self.properties['linestyle'],
                alpha=self.properties['alpha'],
                picker=True,
                pickradius=5
            )
        
        # Add to the current axes
        try:
            if hasattr(self.canvas, 'figure') and self.canvas.figure.axes:
                # Use the primary axis (first one) but ensure it spans the full plot area
                ax = self.canvas.figure.axes[0]
                if (self.line_type == 'arrow' and self.properties['arrow_style'] != 'none' and
                    isinstance(self.artist, FancyArrowPatch)):
                    ax.add_patch(self.artist)
                elif isinstance(self.artist, Line2D):
                    ax.add_line(self.artist)

                # Ensure the line is drawn on top of other elements
                if hasattr(self.artist, 'set_zorder'):
                    self.artist.set_zorder(10)  # High z-order to appear on top

                self.canvas.draw_idle()
        except Exception as e:
            print(f"[ERROR] Failed to add artist to axes: {e}")
    
    def update_artist(self):
        """Update the visual appearance of the artist"""
        try:
            # Check if we need to handle 'none' arrow style
            if (self.line_type == 'arrow' and self.properties['arrow_style'] == 'none' and
                isinstance(self.artist, FancyArrowPatch)):
                # Need to convert from arrow to line
                try:
                    self.artist.remove()
                except ValueError:
                    pass  # Already removed
                self.create_artist()
                return
            elif (self.line_type == 'arrow' and self.properties['arrow_style'] != 'none' and
                  isinstance(self.artist, Line2D)):
                # Need to convert from line to arrow
                try:
                    self.artist.remove()
                except ValueError:
                    pass  # Already removed
                self.create_artist()
                return

            if self.line_type == 'arrow' and isinstance(self.artist, FancyArrowPatch):
                # For arrows, we need to update the patch properties
                self.artist.set_positions(self.start_point, self.end_point)
                if self.properties['arrow_style'] != 'none':
                    self.artist.set_arrowstyle(self.properties['arrow_style'])
                self.artist.set_color(self.properties['color'])
                self.artist.set_linewidth(self.properties['linewidth'])
                self.artist.set_linestyle(self.properties['linestyle'])
                self.artist.set_alpha(self.properties['alpha'])
                self.artist.set_mutation_scale(self.properties['arrow_size'])
                self.artist.set_connectionstyle(self.properties['connection_style'])
                self.canvas.draw_idle()
            elif self.line_type == 'line' and isinstance(self.artist, Line2D):
                # Update line (handles both line_type='line' and arrow_style='none')
                self.artist.set_data([self.start_point[0], self.end_point[0]],
                                   [self.start_point[1], self.end_point[1]])
                self.artist.set_color(self.properties['color'])
                self.artist.set_linewidth(self.properties['linewidth'])
                self.artist.set_linestyle(self.properties['linestyle'])
                self.artist.set_alpha(self.properties['alpha'])
                self.canvas.draw_idle()
            else:
                # If type mismatch, recreate the artist
                if self.artist:
                    try:
                        self.artist.remove()
                    except ValueError:
                        pass  # Already removed
                self.create_artist()
        except Exception as e:
            print(f"[ERROR] Failed to update artist: {e}")
            # Try to recreate the artist as fallback
            try:
                if self.artist:
                    try:
                        self.artist.remove()
                    except ValueError:
                        pass  # Already removed
                self.create_artist()
            except Exception as e2:
                print(f"[ERROR] Failed to recreate artist: {e2}")
    
    def handle_pick(self, event):
        """Handle pick events when this line is clicked"""
        # Handle both PickEvent and MouseEvent
        if hasattr(event, 'artist'):
            # This is a PickEvent
            if event.artist == self.artist and self.connected:
                print(f"[DEBUG] Line {self.line_id} PICKED!")

                # Determine what part was clicked
                click_point = (event.mouseevent.xdata, event.mouseevent.ydata)
                if click_point[0] is None or click_point[1] is None:
                    return False

                mouse_event = event.mouseevent
        else:
            # This is a MouseEvent (from our custom handling)
            if not self.connected:
                return False

            print(f"[DEBUG] Line {self.line_id} PICKED!")

            # Determine what part was clicked
            click_point = (event.xdata, event.ydata)
            if click_point[0] is None or click_point[1] is None:
                return False

            mouse_event = event
            
            # Calculate distances to start, end, and line
            start_dist = np.sqrt((click_point[0] - self.start_point[0])**2 + 
                               (click_point[1] - self.start_point[1])**2)
            end_dist = np.sqrt((click_point[0] - self.end_point[0])**2 + 
                             (click_point[1] - self.end_point[1])**2)
            
            # Threshold for point selection (in data coordinates)
            threshold = self._get_selection_threshold()
            
            if start_dist < threshold:
                self.dragging_point = 'start'
            elif end_dist < threshold:
                self.dragging_point = 'end'
            else:
                self.dragging_point = 'line'
            
            self.press = (click_point, mouse_event.x, mouse_event.y)
            self._dragging = True  # Set dragging flag

            # Set this line as selected
            self.set_selected(True)

            print(f"[DEBUG] Dragging: {self.dragging_point}")
            return True
        return False
    
    def handle_motion(self, event):
        """Handle mouse motion events"""
        if self.press is None or not self.connected:
            return False
        
        if event.xdata is None or event.ydata is None:
            return False
        
        try:
            (x0, y0), xpress, ypress = self.press
            dx = event.xdata - x0
            dy = event.ydata - y0

            # Apply orientation constraints to movement
            constrained_dx, constrained_dy = self._constrain_movement(dx, dy)

            if self.dragging_point == 'start':
                self.start_point = (self.start_point[0] + constrained_dx, self.start_point[1] + constrained_dy)
                # Apply orientation constraint after moving start point
                if self.orientation_constraint != 'free':
                    self._apply_orientation_constraint()
            elif self.dragging_point == 'end':
                self.end_point = (self.end_point[0] + constrained_dx, self.end_point[1] + constrained_dy)
                # Apply orientation constraint after moving end point
                if self.orientation_constraint != 'free':
                    self._apply_orientation_constraint()
            elif self.dragging_point == 'line':
                # Move entire line
                self.start_point = (self.start_point[0] + constrained_dx, self.start_point[1] + constrained_dy)
                self.end_point = (self.end_point[0] + constrained_dx, self.end_point[1] + constrained_dy)

            self.update_artist()
            self.press = ((event.xdata, event.ydata), xpress, ypress)
            return True
            
        except Exception as e:
            print(f"[DEBUG] Error in line motion: {e}")
            return False
    
    def handle_release(self, event):
        """Handle mouse release events"""
        if self.press is None or not self.connected:
            return False
        
        print(f"[DEBUG] Line {self.line_id} released")
        self.press = None
        self.dragging_point = None
        self._dragging = False  # Clear dragging flag
        self.canvas.draw_idle()
        return True
    
    def _get_selection_threshold(self):
        """Get the threshold for point selection based on current zoom level"""
        if hasattr(self.canvas, 'figure') and self.canvas.figure.axes:
            ax = self.canvas.figure.axes[0]
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()
            # Use 1% of the smaller axis range as threshold (reduced from 2% for more precision)
            threshold = min(abs(xlim[1] - xlim[0]), abs(ylim[1] - ylim[0])) * 0.01
            # Ensure minimum threshold for usability
            return max(threshold, 0.5)
        return 1.0  # Default threshold

    def contains_point(self, point: Tuple[float, float], tolerance: Optional[float] = None) -> bool:
        """Check if a point is near this line"""
        if tolerance is None:
            tolerance = self._get_selection_threshold()

        x, y = point
        x1, y1 = self.start_point
        x2, y2 = self.end_point

        # Calculate distance from point to line segment
        # Using the formula for distance from point to line segment
        A = x - x1
        B = y - y1
        C = x2 - x1
        D = y2 - y1

        dot = A * C + B * D
        len_sq = C * C + D * D

        if len_sq == 0:
            # Line is actually a point
            distance = np.sqrt(A * A + B * B)
        else:
            param = dot / len_sq

            if param < 0:
                # Point is closest to start point
                distance = np.sqrt(A * A + B * B)
            elif param > 1:
                # Point is closest to end point
                xx = x - x2
                yy = y - y2
                distance = np.sqrt(xx * xx + yy * yy)
            else:
                # Point is closest to somewhere on the line segment
                xx = x - (x1 + param * C)
                yy = y - (y1 + param * D)
                distance = np.sqrt(xx * xx + yy * yy)

        return distance <= tolerance
    
    def connect(self):
        """Enable interaction"""
        self.connected = True
        print(f"[DEBUG] Connected line {self.line_id}")
    
    def disconnect(self):
        """Disable interaction"""
        self.connected = False
        print(f"[DEBUG] Disconnected line {self.line_id}")
    
    def remove(self):
        """Remove the line from the plot"""
        if self.artist:
            try:
                self.artist.remove()
                self.canvas.draw_idle()
            except ValueError as e:
                # Artist might already be removed
                print(f"[DEBUG] Artist already removed: {e}")
            except Exception as e:
                print(f"[ERROR] Failed to remove artist: {e}")
    
    def update_properties(self, **kwargs):
        """Update line properties"""
        # Add option to skip auto-save (for internal updates)
        skip_auto_save = kwargs.pop('_skip_auto_save', False)

        # Debug logging can be enabled for troubleshooting if needed
        # if kwargs and not skip_auto_save:
        #     print(f"[DEBUG] Line {self.line_id} properties being updated: {kwargs}")

        # Check if arrow_style is changing to/from 'none'
        old_arrow_style = self.properties.get('arrow_style', 'none')
        self.properties.update(kwargs)
        new_arrow_style = self.properties.get('arrow_style', 'none')

        # If arrow style changed to/from 'none', we need to recreate the artist
        if ((old_arrow_style == 'none') != (new_arrow_style == 'none')):
            try:
                if self.artist:
                    self.artist.remove()
            except ValueError:
                pass  # Already removed
            self.create_artist()
        else:
            self.update_artist()

        # Auto-save these properties as defaults for future lines (unless skipped)
        if not skip_auto_save:
            self._save_as_defaults(kwargs)
    
    def get_properties(self):
        """Get current line properties"""
        return self.properties.copy()

    def debug_properties(self, context=""):
        """Debug method to print current properties"""
        print(f"[DEBUG] Line {self.line_id} properties {context}: {self.properties}")

    def set_selected(self, selected: bool):
        """Set selection state with visual feedback"""
        if selected == self.selected:
            return

        self.selected = selected

        if selected:
            # Store original properties
            self.original_properties = self.properties.copy()
            # Apply selection highlighting
            highlight_props = self.properties.copy()
            # highlight_props['linewidth'] = self.properties['linewidth'] + 1
            highlight_props['alpha'] = min(1.0, self.properties['alpha'] + 0.3)
            self.update_properties(**highlight_props)
        else:
            # Restore original properties
            if self.original_properties:
                self.update_properties(**self.original_properties)
                self.original_properties = {}

    def is_selected(self) -> bool:
        """Check if line is selected"""
        return self.selected

    def delete(self):
        """Delete this line"""
        if self.main_window and hasattr(self.main_window, 'line_drawing_manager'):
            self.main_window.line_drawing_manager.delete_line(self)
        else:
            self.remove()
            self.disconnect()

    def set_orientation_constraint(self, constraint: str):
        """Set orientation constraint for this line"""
        if constraint in ['free', 'horizontal', 'vertical']:
            self.orientation_constraint = constraint
            # Apply constraint to current line position
            self._apply_orientation_constraint()

    def get_orientation_constraint(self) -> str:
        """Get current orientation constraint"""
        return self.orientation_constraint

    def _apply_orientation_constraint(self):
        """Apply orientation constraint to current line"""
        if self.orientation_constraint == 'horizontal':
            # Make line horizontal by adjusting end point Y to match start point Y
            self.end_point = (self.end_point[0], self.start_point[1])
        elif self.orientation_constraint == 'vertical':
            # Make line vertical by adjusting end point X to match start point X
            self.end_point = (self.start_point[0], self.end_point[1])
        # 'free' constraint doesn't change anything

        self.update_artist()

    def _constrain_movement(self, dx: float, dy: float) -> Tuple[float, float]:
        """Apply movement constraints based on orientation"""
        if self.orientation_constraint == 'horizontal':
            return (dx, 0)  # Only allow horizontal movement
        elif self.orientation_constraint == 'vertical':
            return (0, dy)  # Only allow vertical movement
        else:
            return (dx, dy)  # Free movement

    def show_context_menu(self, event):
        """Show context menu for line operations"""
        try:
            from PySide6.QtWidgets import QMenu
            from PySide6.QtGui import QAction
            from PySide6.QtCore import QPoint

            if not self.main_window:
                return

            menu = QMenu(self.main_window)

            # Line properties action
            properties_action = QAction("Properties...", menu)
            properties_action.triggered.connect(self._show_properties_dialog)
            menu.addAction(properties_action)

            menu.addSeparator()

            # Orientation actions
            orientation_menu = menu.addMenu("Orientation")

            free_action = QAction("Free", orientation_menu)
            free_action.setCheckable(True)
            free_action.setChecked(self.orientation_constraint == 'free')
            free_action.triggered.connect(lambda: self.set_orientation_constraint('free'))
            orientation_menu.addAction(free_action)

            horizontal_action = QAction("Horizontal", orientation_menu)
            horizontal_action.setCheckable(True)
            horizontal_action.setChecked(self.orientation_constraint == 'horizontal')
            horizontal_action.triggered.connect(lambda: self.set_orientation_constraint('horizontal'))
            orientation_menu.addAction(horizontal_action)

            vertical_action = QAction("Vertical", orientation_menu)
            vertical_action.setCheckable(True)
            vertical_action.setChecked(self.orientation_constraint == 'vertical')
            vertical_action.triggered.connect(lambda: self.set_orientation_constraint('vertical'))
            orientation_menu.addAction(vertical_action)

            menu.addSeparator()

            # Delete action
            delete_action = QAction("Delete", menu)
            delete_action.triggered.connect(self.delete)
            menu.addAction(delete_action)

            # Show menu at cursor position
            global_pos = self.main_window.mapToGlobal(QPoint(int(event.x), int(event.y)))
            menu.exec(global_pos)

        except Exception as e:
            print(f"[ERROR] Failed to show context menu: {e}")

    def _show_properties_dialog(self):
        """Show properties dialog for this line"""
        if self.main_window and hasattr(self.main_window, 'line_drawing_manager'):
            self.main_window.line_drawing_manager.show_settings_dialog(self)

    def _save_as_defaults(self, updated_props):
        """Save updated properties as defaults for future lines"""
        if self.main_window and hasattr(self.main_window, 'line_drawing_manager'):
            # Don't auto-save during line creation to prevent interference
            if getattr(self.main_window.line_drawing_manager, '_creating_line', False):
                return

            # Don't auto-save during dragging to prevent interference
            if self._dragging:
                return

            # Filter to only include line-related properties
            line_props = {k: v for k, v in updated_props.items()
                         if k in ['color', 'linewidth', 'linestyle', 'alpha', 'arrow_style', 'arrow_size', 'connection_style']}

            if line_props:  # Only update if there are relevant properties
                self.main_window.line_drawing_manager.update_default_properties(**line_props)
    
    def set_line_type(self, line_type):
        """Change between line and arrow"""
        if line_type != self.line_type:
            self.line_type = line_type
            if self.artist:
                self.artist.remove()
            self.create_artist()
            # Re-add to axes
            if hasattr(self.canvas, 'figure') and self.canvas.figure.axes:
                ax = self.canvas.figure.axes[0]
                if self.line_type == 'arrow':
                    ax.add_patch(self.artist)
                else:
                    ax.add_line(self.artist)
                self.canvas.draw_idle()

