#!/usr/bin/env python3
"""
Test script to verify the circular dependency fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_toolbar_initialization():
    """Test that the toolbar can be initialized without circular dependency errors"""
    print("Testing CustomNavigationToolbar initialization...")
    
    try:
        from gui.dialog.custom_plot_settings import CustomNavigationToolbar
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
        from matplotlib.figure import Figure
        
        # Create a simple figure and canvas like in the real application
        fig = Figure(figsize=(8, 6))
        ax = fig.add_subplot(111)
        ax.plot([1, 2, 3, 4], [1, 4, 2, 3], label='Test Data')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_title('Test Plot')
        ax.legend()
        
        # Create canvas
        canvas = FigureCanvas(fig)
        
        # Create mock parent widget
        class MockParent:
            def __init__(self):
                pass
            def mapToGlobal(self, point):
                return point
        
        parent = MockParent()
        
        # Create sample data series like in the real application
        data_series = {
            'data_frame_1': {
                'Test Data': [1, 4, 2, 3]
            }
        }
        
        # This should work without circular dependency errors
        toolbar = CustomNavigationToolbar(canvas, parent, data_series)
        
        print("✓ Toolbar created successfully")
        
        # Check that all managers are properly initialized
        if hasattr(toolbar, 'annotation_manager') and toolbar.annotation_manager:
            print("✓ AnnotationManager initialized")
        else:
            print("✗ AnnotationManager not initialized")
            return False
            
        if hasattr(toolbar, 'line_drawing_manager') and toolbar.line_drawing_manager:
            print("✓ LineDrawingManager initialized")
        else:
            print("✗ LineDrawingManager not initialized")
            return False
            
        if hasattr(toolbar, 'mock_main_window') and toolbar.mock_main_window:
            print("✓ Mock main window created")
        else:
            print("✗ Mock main window not created")
            return False
            
        # Check that the circular reference is properly resolved
        if (toolbar.mock_main_window.line_drawing_manager and 
            toolbar.mock_main_window.line_drawing_manager == toolbar.line_drawing_manager):
            print("✓ Mock main window has correct line_drawing_manager reference")
        else:
            print("✗ Mock main window line_drawing_manager reference not set correctly")
            return False
            
        # Check that event handlers are connected
        if hasattr(toolbar, 'event_handlers') and len(toolbar.event_handlers) > 0:
            print(f"✓ Event handlers connected: {len(toolbar.event_handlers)}")
        else:
            print("✗ Event handlers not connected")
            return False
        
        # Test that the toolbar methods work
        required_methods = [
            'toggle_line_drawing_mode',
            'toggle_annotation_mode',
            'show_line_context_menu',
            'show_line_style_dialog',
            'show_annotation_context_menu'
        ]
        
        for method_name in required_methods:
            if hasattr(toolbar, method_name) and callable(getattr(toolbar, method_name)):
                print(f"✓ {method_name} method available")
            else:
                print(f"✗ {method_name} method not available")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Toolbar initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mock_main_window_interface():
    """Test that the mock main window provides the correct interface"""
    print("\nTesting mock main window interface...")
    
    try:
        from gui.dialog.custom_plot_settings import CustomNavigationToolbar
        from matplotlib.figure import Figure
        from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
        
        # Create minimal setup
        fig = Figure()
        canvas = FigureCanvas(fig)
        
        class MockParent:
            def mapToGlobal(self, point):
                return point
        
        toolbar = CustomNavigationToolbar(canvas, MockParent(), {})
        mock_main_window = toolbar.mock_main_window
        
        # Test required attributes
        required_attributes = [
            'current_canvas',
            'annotation_drag_mode',
            'ui',
            'line_drawing_manager'
        ]
        
        for attr in required_attributes:
            if hasattr(mock_main_window, attr):
                print(f"✓ Mock main window has {attr}")
            else:
                print(f"✗ Mock main window missing {attr}")
                return False
        
        # Test required methods
        required_methods = [
            'mapToGlobal',
            'register_draggable_line',
            'register_draggable_annotation'
        ]
        
        for method in required_methods:
            if hasattr(mock_main_window, method) and callable(getattr(mock_main_window, method)):
                print(f"✓ Mock main window has {method} method")
            else:
                print(f"✗ Mock main window missing {method} method")
                return False
        
        # Test UI interface
        if hasattr(mock_main_window.ui, 'lblLogInfo'):
            print("✓ Mock UI has lblLogInfo")
        else:
            print("✗ Mock UI missing lblLogInfo")
            return False
            
        if hasattr(mock_main_window.ui.lblLogInfo, 'setText'):
            print("✓ Mock label has setText method")
        else:
            print("✗ Mock label missing setText method")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Mock main window interface test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all circular dependency fix tests"""
    print("Testing Circular Dependency Fix...")
    print("=" * 40)
    
    tests = [
        test_toolbar_initialization,
        test_mock_main_window_interface
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("✅ All tests passed! Circular dependency issue fixed!")
        print("\nThe CustomNavigationToolbar should now work correctly in your application.")
        print("The mock_main_window provides a lightweight interface for the managers")
        print("without requiring the full main window functionality.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    run_all_tests()
