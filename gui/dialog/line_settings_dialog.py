"""
Line Settings Dialog
Comprehensive dialog for configuring line properties and global preferences
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QPushButton, QColorDialog, QComboBox, QSpinBox, QDoubleSpinBox,
    QCheckBox, QGroupBox, QTabWidget, QWidget, QSlider, QLineEdit
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor


class LineSettingsDialog(QDialog):
    """Comprehensive line settings dialog"""
    
    settings_changed = Signal(dict)  # Emitted when settings are applied
    
    def __init__(self, parent=None, line_manager=None, current_line=None):
        super().__init__(parent)
        self.line_manager = line_manager
        self.current_line = current_line
        self.settings = {}
        
        self.setWindowTitle("Line Settings")
        self.setModal(True)
        self.resize(500, 600)
        
        self.setup_ui()
        self.load_current_settings()
        
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Line Properties Tab
        self.setup_line_properties_tab()
        
        # Global Preferences Tab
        self.setup_global_preferences_tab()
        
        # Orientation Tab
        self.setup_orientation_tab()
        
        # Button layout
        button_layout = QHBoxLayout()
        
        self.apply_btn = QPushButton("Apply")
        self.apply_btn.clicked.connect(self.apply_settings)
        
        self.ok_btn = QPushButton("OK")
        self.ok_btn.clicked.connect(self.accept_settings)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
    def setup_line_properties_tab(self):
        """Setup line properties tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Line Style Group
        style_group = QGroupBox("Line Style")
        style_layout = QGridLayout(style_group)
        
        # Color
        style_layout.addWidget(QLabel("Color:"), 0, 0)
        self.color_btn = QPushButton()
        self.color_btn.setFixedSize(50, 30)
        self.color_btn.clicked.connect(self.choose_color)
        style_layout.addWidget(self.color_btn, 0, 1)
        
        self.color_edit = QLineEdit()
        self.color_edit.setPlaceholderText("#FF0000")
        self.color_edit.textChanged.connect(self.update_color_from_text)
        style_layout.addWidget(self.color_edit, 0, 2)
        
        # Line width
        style_layout.addWidget(QLabel("Width:"), 1, 0)
        self.width_spin = QDoubleSpinBox()
        self.width_spin.setRange(0.1, 10.0)
        self.width_spin.setSingleStep(0.1)
        self.width_spin.setValue(2.0)
        style_layout.addWidget(self.width_spin, 1, 1)
        
        # Line style
        style_layout.addWidget(QLabel("Style:"), 2, 0)
        self.style_combo = QComboBox()
        self.style_combo.addItems(["Solid", "Dashed", "Dotted", "Dash-Dot"])
        style_layout.addWidget(self.style_combo, 2, 1)

        # Line type
        style_layout.addWidget(QLabel("Type:"), 3, 0)
        self.type_combo = QComboBox()
        self.type_combo.addItems(["Line", "Arrow"])
        style_layout.addWidget(self.type_combo, 3, 1)
        
        # Alpha/Opacity
        style_layout.addWidget(QLabel("Opacity:"), 4, 0)
        self.alpha_spin = QDoubleSpinBox()
        self.alpha_spin.setRange(0.0, 1.0)
        self.alpha_spin.setSingleStep(0.1)
        self.alpha_spin.setValue(1.0)
        style_layout.addWidget(self.alpha_spin, 4, 1)
        
        layout.addWidget(style_group)
        
        # Arrow Properties Group
        arrow_group = QGroupBox("Arrow Properties")
        arrow_layout = QGridLayout(arrow_group)
        
        # Arrow style
        arrow_layout.addWidget(QLabel("Arrow Style:"), 0, 0)
        self.arrow_combo = QComboBox()
        self.arrow_combo.addItems(["None", "Start", "End", "Both"])
        arrow_layout.addWidget(self.arrow_combo, 0, 1)
        
        # Arrow size
        arrow_layout.addWidget(QLabel("Arrow Size:"), 1, 0)
        self.arrow_size_spin = QSpinBox()
        self.arrow_size_spin.setRange(5, 50)
        self.arrow_size_spin.setValue(15)
        arrow_layout.addWidget(self.arrow_size_spin, 1, 1)
        
        layout.addWidget(arrow_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Line Properties")
        
    def setup_global_preferences_tab(self):
        """Setup global preferences tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Default Settings Group
        defaults_group = QGroupBox("Default Settings for New Lines")
        defaults_layout = QVBoxLayout(defaults_group)
        
        self.save_as_default_cb = QCheckBox("Save current settings as default for new lines")
        self.save_as_default_cb.setToolTip("Manually save these specific settings as defaults")
        defaults_layout.addWidget(self.save_as_default_cb)
        
        self.auto_apply_cb = QCheckBox("Automatically save changes as defaults for new lines")
        self.auto_apply_cb.setChecked(True)
        self.auto_apply_cb.setToolTip("When enabled, any changes to line properties will automatically become the default for new lines")
        defaults_layout.addWidget(self.auto_apply_cb)
        
        layout.addWidget(defaults_group)
        
        # Interaction Group
        interaction_group = QGroupBox("Interaction Settings")
        interaction_layout = QVBoxLayout(interaction_group)
        
        self.snap_to_grid_cb = QCheckBox("Snap to grid when creating lines")
        interaction_layout.addWidget(self.snap_to_grid_cb)
        
        self.show_handles_cb = QCheckBox("Show selection handles on selected lines")
        self.show_handles_cb.setChecked(True)
        interaction_layout.addWidget(self.show_handles_cb)
        
        layout.addWidget(interaction_group)

        # Status label
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #4CAF50; font-style: italic; margin: 5px;")
        layout.addWidget(self.status_label)

        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Preferences")
        
    def setup_orientation_tab(self):
        """Setup orientation constraints tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Orientation Group
        orient_group = QGroupBox("Line Orientation")
        orient_layout = QVBoxLayout(orient_group)
        
        self.free_orientation_cb = QCheckBox("Free orientation (default)")
        self.free_orientation_cb.setChecked(True)
        orient_layout.addWidget(self.free_orientation_cb)
        
        self.horizontal_only_cb = QCheckBox("Horizontal lines only")
        orient_layout.addWidget(self.horizontal_only_cb)
        
        self.vertical_only_cb = QCheckBox("Vertical lines only")
        orient_layout.addWidget(self.vertical_only_cb)
        
        # Make checkboxes mutually exclusive
        self.free_orientation_cb.toggled.connect(
            lambda checked: self._handle_orientation_change('free', checked))
        self.horizontal_only_cb.toggled.connect(
            lambda checked: self._handle_orientation_change('horizontal', checked))
        self.vertical_only_cb.toggled.connect(
            lambda checked: self._handle_orientation_change('vertical', checked))
        
        layout.addWidget(orient_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Orientation")
        
    def _handle_orientation_change(self, orientation: str, checked: bool):
        """Handle orientation checkbox changes"""
        if not checked:
            return
            
        # Uncheck other orientation options
        if orientation != 'free':
            self.free_orientation_cb.setChecked(False)
        if orientation != 'horizontal':
            self.horizontal_only_cb.setChecked(False)
        if orientation != 'vertical':
            self.vertical_only_cb.setChecked(False)
            
        # Update line manager if available
        if self.line_manager:
            self.line_manager.set_line_orientation(orientation)
            
    def choose_color(self):
        """Open color picker dialog"""
        color = QColorDialog.getColor(QColor(self.color_edit.text() or "#FF0000"), self)
        if color.isValid():
            self.color_edit.setText(color.name())
            self.update_color_button(color.name())
            
    def update_color_from_text(self, text: str):
        """Update color button when text changes"""
        try:
            if text.startswith('#') and len(text) == 7:
                self.update_color_button(text)
        except:
            pass
            
    def update_color_button(self, color_hex: str):
        """Update color button appearance"""
        self.color_btn.setStyleSheet(f"background-color: {color_hex}; border: 1px solid #ccc;")
        
    def load_current_settings(self):
        """Load current settings from line manager or current line"""
        if self.current_line:
            # Load from specific line
            props = self.current_line.get_properties()
            # Debug: print(f"[DEBUG] Dialog loading line properties: {props}")
            self.color_edit.setText(props.get('color', '#FF0000'))
            self.width_spin.setValue(props.get('linewidth', 2.0))
            self.alpha_spin.setValue(props.get('alpha', 1.0))
            
            # Map line style
            style_map = {'-': 0, '--': 1, ':': 2, '-.': 3}
            style_index = style_map.get(props.get('linestyle', '-'), 0)
            self.style_combo.setCurrentIndex(style_index)
            
            # Map arrow style
            arrow_map = {'none': 0, '<-': 1, '->': 2, '<->': 3}
            arrow_index = arrow_map.get(props.get('arrow_style', 'none'), 0)
            self.arrow_combo.setCurrentIndex(arrow_index)
            
            self.arrow_size_spin.setValue(props.get('arrow_size', 15))

            # Set line type
            type_index = 0 if self.current_line.line_type == 'line' else 1
            self.type_combo.setCurrentIndex(type_index)
            
        elif self.line_manager:
            # Load from line manager defaults
            defaults = self.line_manager.get_default_properties()
            self.color_edit.setText(defaults.get('color', '#FF0000'))
            self.width_spin.setValue(defaults.get('linewidth', 2.0))
            self.alpha_spin.setValue(defaults.get('alpha', 1.0))
            
            # Set orientation
            orientation = self.line_manager.get_line_orientation()
            if orientation == 'horizontal':
                self.horizontal_only_cb.setChecked(True)
            elif orientation == 'vertical':
                self.vertical_only_cb.setChecked(True)
            else:
                self.free_orientation_cb.setChecked(True)
        
        # Update color button
        self.update_color_button(self.color_edit.text())

    def showEvent(self, event):
        """Override showEvent to refresh settings when dialog is shown"""
        super().showEvent(event)
        # Refresh the settings every time the dialog is shown
        # Debug: print(f"[DEBUG] Dialog showEvent - refreshing settings")
        self.load_current_settings()
        # Debug: print(f"[DEBUG] Dialog shown, refreshed settings")

    def exec(self):
        """Override exec to ensure settings are loaded before showing"""
        # Debug: print(f"[DEBUG] Dialog exec - loading current settings")
        self.load_current_settings()
        return super().exec()
        
    def get_current_settings(self) -> Dict[str, Any]:
        """Get current settings from UI"""
        style_map = ["-", "--", ":", "-."]
        arrow_map = ["none", "<-", "->", "<->"]
        type_map = ["line", "arrow"]

        return {
            'color': self.color_edit.text(),
            'linewidth': self.width_spin.value(),
            'linestyle': style_map[self.style_combo.currentIndex()],
            'alpha': self.alpha_spin.value(),
            'arrow_style': arrow_map[self.arrow_combo.currentIndex()],
            'arrow_size': self.arrow_size_spin.value(),
            'line_type': type_map[self.type_combo.currentIndex()],
            'save_as_default': self.save_as_default_cb.isChecked(),
            'auto_apply': self.auto_apply_cb.isChecked(),
            'snap_to_grid': self.snap_to_grid_cb.isChecked(),
            'show_handles': self.show_handles_cb.isChecked()
        }
        
    def apply_settings(self):
        """Apply current settings"""
        settings = self.get_current_settings()
        
        # Apply to current line if available
        if self.current_line:
            line_props = {k: v for k, v in settings.items()
                         if k in ['color', 'linewidth', 'linestyle', 'alpha', 'arrow_style', 'arrow_size']}
            # Debug: print(f"[DEBUG] Dialog applying properties to line: {line_props}")
            self.current_line.update_properties(**line_props)
            # Debug: print(f"[DEBUG] Line properties after apply: {self.current_line.get_properties()}")

            # Handle line type change if needed
            if 'line_type' in settings and settings['line_type'] != self.current_line.line_type:
                self.current_line.line_type = settings['line_type']
                self.current_line.create_artist()  # Recreate artist with new type
        
        # Update line manager defaults if requested or auto-apply is enabled
        if self.line_manager and (settings.get('save_as_default', False) or settings.get('auto_apply', True)):
            line_props = {k: v for k, v in settings.items()
                         if k in ['color', 'linewidth', 'linestyle', 'alpha', 'arrow_style', 'arrow_size']}
            self.line_manager.update_default_properties(**line_props)
            print(f"[DEBUG] Updated default properties: {line_props}")

            # Show status feedback
            if settings.get('auto_apply', True):
                self.status_label.setText("✓ Settings automatically saved as defaults for new lines")
            else:
                self.status_label.setText("✓ Settings saved as defaults")
        else:
            self.status_label.setText("Settings applied to current line only")

        self.settings_changed.emit(settings)
        
    def accept_settings(self):
        """Apply settings and close dialog"""
        self.apply_settings()
        self.accept()

    def refresh_from_line(self):
        """Force refresh dialog values from current line"""
        if self.current_line:
            # Debug: print(f"[DEBUG] Force refreshing dialog from line {self.current_line.line_id}")
            self.load_current_settings()
        else:
            pass  # Debug: print(f"[DEBUG] No current line to refresh from")
