import os
import json
from pathlib import Path

from src.features import LineDrawingManager, AnnotationManager
from .annotation_dialog import AnnotationEditDialog


# ---- Persistence helpers (added by patch) ----
def _profiles_path() -> Path:
    base = Path(os.getenv("APPDATA") or (Path.home() / ".local" / "share"))
    return base / "plottool" / "profiles.json"


def _ensure_parent(path: Path):
    p = path if isinstance(path, Path) else Path(path)
    p.parent.mkdir(parents=True, exist_ok=True)


import logging
from typing import Dict, Any, Optional, List, Tuple

import numpy as np
from PIL import ImageFont
from PySide6 import QtGui, QtWidgets
from PySide6.QtGui import QIcon, QFont, QFontMetricsF
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
from PySide6.QtWidgets import QApplication, QMainWindow, QDialog, Q<PERSON>ush<PERSON>utton, QColorDialog, QFileDialog, QToolButton, \
    QMenu
from PySide6.QtCore import Qt, QSize, QPoint, QTimer, QEvent
from PySide6 import QtCore
from matplotlib.font_manager import FontProperties
from scipy.stats import linregress

from .ui_plot_settings import Ui_Plot_settings_Dialog
from src.utils import get_resource_path
from .. import MultiStateToggleSwitch

# Set up logging
logger = logging.getLogger(__name__)


class PlotSettingsManager:
    """Centralized manager for plot settings and data frame operations"""

    def __init__(self):
        self.settings: Dict[str, Any] = {}
        self.data_frame_1: Dict = {}
        self.data_frame_2: Dict = {}

    def initialize_data_frames(self, data_frame: Any) -> Tuple[Dict, Dict]:
        """Safely initialize data frames from input parameter"""
        print(f"Debug: initialize_data_frames called with type: {type(data_frame)}")

        if not isinstance(data_frame, dict):
            print(f"Debug: data_frame is not dict, returning empty dicts")
            return data_frame or {}, {}

        print(f"Debug: data_frame keys: {list(data_frame.keys())}")

        # Try different extraction strategies
        df1 = None
        df2 = None

        # Strategy 1: Look for explicit data_frame_1 and data_frame_2 keys
        df1 = self._extract_data_frame(data_frame, 'data_frame_1')
        df2 = self._extract_data_frame(data_frame, 'data_frame_2')

        # Strategy 2: If no explicit frames found, try to use the entire data_frame as df1
        if not df1:
            # Check if data_frame contains series-like data directly
            if self._contains_series_data(data_frame):
                print("Debug: Using entire data_frame as data_frame_1")
                df1 = data_frame
            else:
                # Strategy 3: Look for nested structures that might contain series data
                for key, value in data_frame.items():
                    if isinstance(value, dict) and self._contains_series_data(value):
                        print(f"Debug: Found series data in key '{key}'")
                        if not df1:
                            df1 = value
                        elif not df2:
                            df2 = value
                            break

        self.data_frame_1 = df1 or {}
        self.data_frame_2 = df2 or {}

        print(f"Debug: Final data_frame_1 keys: {list(self.data_frame_1.keys())}")
        print(f"Debug: Final data_frame_2 keys: {list(self.data_frame_2.keys())}")

        return self.data_frame_1, self.data_frame_2

    def _extract_data_frame(self, data_frame: Dict, key: str) -> Optional[Dict]:
        """Extract specific data frame from nested structure"""
        if key in data_frame:
            return data_frame[key]

        # Look in nested dictionaries
        for value in data_frame.values():
            if isinstance(value, dict) and key in value:
                return value[key]

        return None

    def _contains_series_data(self, data_dict: Dict) -> bool:
        """Check if a dictionary contains data that looks like time series"""
        try:
            if not isinstance(data_dict, dict) or not data_dict:
                return False

            # Check if values look like data series (lists, arrays, etc.)
            for key, value in data_dict.items():
                if isinstance(value, (list, tuple, np.ndarray)):
                    if len(value) > 1:  # Need at least 2 data points
                        return True
                elif hasattr(value, 'values') and hasattr(value, '__len__'):
                    # Pandas Series/DataFrame
                    if len(value) > 1:
                        return True
                elif hasattr(value, '__iter__') and not isinstance(value, (str, bytes, dict)):
                    # Other iterable types
                    try:
                        if len(list(value)) > 1:
                            return True
                    except:
                        pass
            return False
        except Exception as e:
            print(f"Debug: Error checking series data: {e}")
            return False

    def safe_convert(self, value: Any, target_type: type, default: Any) -> Any:
        """Safely convert value to target type with fallback"""
        try:
            if value is None:
                return default
            if target_type == int:
                return int(float(value))  # Handle string numbers
            return target_type(value)
        except (ValueError, TypeError):
            return default

    def extract_plot_settings(self, ax, ax_2=None) -> Dict[str, Any]:
        """Extract current plot settings into structured dictionary"""
        settings = {}

        try:
            # Title settings
            title = ax.title
            settings.update({
                'title_font': title.get_fontname() or 'Arial',
                'title_size': self.safe_convert(title.get_fontsize(), int, 12),
                'title_color': title.get_color() or '#000000'
            })

            # Label settings
            xlabel = ax.xaxis.get_label()
            settings.update({
                'label_font': xlabel.get_fontname() or 'Arial',
                'label_size': self.safe_convert(xlabel.get_fontsize(), int, 10),
                'label_color': xlabel.get_color() or '#000000'
            })

            # Legend settings
            if ax.legend_ is not None:
                legend_texts = ax.legend_.get_texts()
                if legend_texts:
                    text = legend_texts[0]
                    settings.update({
                        'legend_font': text.get_fontname() or 'Arial',
                        'legend_size': self.safe_convert(text.get_fontsize(), int, 10),
                        'legend_color': text.get_color() or '#000000'
                    })

            # Axis limits
            x_min, x_max = ax.get_xlim()
            y_min, y_max = ax.get_ylim()
            settings.update({
                'x_axis_min': x_min,
                'x_axis_max': x_max,
                'y_axis_min': y_min,
                'y_axis_max': y_max
            })

            if ax_2:
                y2_min, y2_max = ax_2.get_ylim()
                settings.update({
                    'y2_axis_min': y2_min,
                    'y2_axis_max': y2_max
                })

            # Data series settings
            self._extract_series_settings(ax, settings, self.data_frame_1)
            if ax_2:
                self._extract_series_settings(ax_2, settings, self.data_frame_2)

            # Grid settings
            settings.update({
                'major_grid_visible': self._check_grid_visibility(ax, 'major'),
                'minor_grid_visible': self._check_grid_visibility(ax, 'minor'),
                'major_grid_color': self._get_grid_color(ax, 'major'),
                'minor_grid_color': self._get_grid_color(ax, 'minor')
            })

            print(f"The minor grid visibility is: {settings['minor_grid_visible']}")

        except Exception as e:
            logger.error(f"Error extracting plot settings: {e}")

        self.settings = settings
        return settings

    def _extract_series_settings(self, ax, settings: Dict, data_frame: Dict):
        """Extract settings for data series from axis"""
        for line in ax.get_lines():
            series_name = line.get_label()
            if series_name and series_name in data_frame:
                settings[f'data_series_color_{series_name}'] = line.get_color() or '#008000'
                if f'legend_name_{series_name}' not in settings:
                    settings[f'legend_name_{series_name}'] = series_name

    def _check_grid_visibility(self, ax, which: str = 'major') -> bool:
        """Check if grid is visible for major or minor ticks."""
        try:
            if which == 'major':
                xticks = ax.xaxis.get_major_ticks()
                yticks = ax.yaxis.get_major_ticks()
            else:
                xticks = ax.xaxis.get_minor_ticks()
                yticks = ax.yaxis.get_minor_ticks()

            # Gather gridlines from tick objects
            lines = []
            for tick in xticks + yticks:
                line_x = tick.gridline
                line_y = getattr(tick, '_gridline2', None)  # depends on backend
                for line in (line_x, line_y):
                    if line and line.get_visible():
                        lines.append(line)

            return len(lines) > 0
        except Exception as e:
            logger.warning(f"Error checking {which} grid visibility: {e}")
            return False

    def _get_grid_color(self, ax, which: str = 'major') -> str:
        """Get gridline color for major or minor grid."""
        try:
            # Force rendering to make sure all gridlines exist
            ax.figure.canvas.draw()

            # Select the correct ticks
            if which == 'major':
                xticks = ax.xaxis.get_major_ticks()
                yticks = ax.yaxis.get_major_ticks()
            else:
                xticks = ax.xaxis.get_minor_ticks()
                yticks = ax.yaxis.get_minor_ticks()

            for tick in xticks + yticks:
                # Try both gridlines (some versions have _gridline2)
                for line in (getattr(tick, 'gridline', None), getattr(tick, '_gridline2', None)):
                    if line and line.get_visible():
                        return line.get_color()
        except Exception as e:
            logger.warning(f"Error getting {which} grid color: {e}")

        # Default fallback
        return '#008000'


class StylesheetManager:
    """Centralized stylesheet management"""

    @staticmethod
    def get_base_paths() -> Dict[str, str]:
        """Get all required asset paths"""
        return {
            'down_arrow': get_resource_path("assets/down-arrow.png").replace('\\', '/'),
            'up_arrow': get_resource_path("assets/up-arrow.png").replace('\\', '/'),
            'checkbox_checked': get_resource_path("assets/checkbox_checked.png").replace('\\', '/'),
            'checkbox_unchecked': get_resource_path("assets/checkbox_unchecked.png").replace('\\', '/'),
        }

    @classmethod
    def get_toolbar_stylesheet(cls) -> str:
        """Generate stylesheet for toolbar"""
        paths = cls.get_base_paths()
        return f"""
            QDialog {{
                background-color: #171717;
            }}
            QLabel {{
                color: black;
                padding: 1px 50px;
                font-family: Arial;
                font-size: 14px;
            }}
            QLineEdit {{
                background-color: #1C1C1C;
                border: 1px solid #303030;
                border-radius: 10px;
                color: #EEEEEE;
                font-size: 16px;
                padding: 4px 15px;
            }}
            {cls._get_combobox_style(paths['down_arrow'])}
            {cls._get_checkbox_style(paths['checkbox_checked'], paths['checkbox_unchecked'])}
            {cls._get_tab_style()}
        """

    @classmethod
    def get_dialog_stylesheet(cls) -> str:
        """Generate stylesheet for dialogs"""
        paths = cls.get_base_paths()
        return f"""
            QDialog {{
                background-color: #171717;
            }}
            QWidget {{
                background-color: #171717;
                color: #EEEEEE;
            }}
            QPushButton {{
                background-color: #1C1C1C;
                color: white;
                font-family: inter;
                font-size: 14px;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #252525;
            }}
            QLineEdit {{
                background-color: #1C1C1C;
                border: 1px solid #303030;
                border-radius: 10px;
                color: #EEEEEE;
                font-size: 16px;
                padding: 4px 15px;
            }}
            {cls._get_combobox_style(paths['down_arrow'])}
            {cls._get_spinbox_style(paths['up_arrow'], paths['down_arrow'])}
            {cls._get_checkbox_style(paths['checkbox_checked'], paths['checkbox_unchecked'])}
            QLabel {{
                color: #AAAAAA;
                font-size: 16px;
                font-family: inter;
            }}
        """

    @staticmethod
    def _get_combobox_style(down_arrow_path: str) -> str:
        """Get combobox specific styles"""
        return f"""
            QComboBox {{
                border: 1px solid #303030;
                border-radius: 10px;
                padding: 4px 15px;
                background: #1C1C1C;
                color: #EEEEEE;
                font-size: 16px;
            }}
            QComboBox::drop-down {{
                border: none;
                background: transparent;
                width: 20px;
                margin-right: 8px;
            }}
            QComboBox::down-arrow {{
                image: url("{down_arrow_path}");
                width: 24px;
                height: 24px;
            }}
        """

    @staticmethod
    def _get_spinbox_style(up_arrow_path: str, down_arrow_path: str) -> str:
        """Get spinbox specific styles"""
        return f"""
            QDoubleSpinBox {{
                border: 1px solid #303030;
                border-radius: 10px;
                padding: 4px 15px;
                background: #1C1C1C;
                color: #EEEEEE;
                font-size: 16px;
            }}
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {{
                border: none;
                background: transparent;
                width: 20px;
                margin-right: 8px;
            }}
            QDoubleSpinBox::up-arrow {{
                image: url("{up_arrow_path}");
                width: 24px;
                height: 24px;
            }}
            QDoubleSpinBox::down-arrow {{
                image: url("{down_arrow_path}");
                width: 24px;
                height: 24px;
            }}
        """

    @staticmethod
    def _get_checkbox_style(checked_path: str, unchecked_path: str) -> str:
        """Get checkbox specific styles"""
        return f"""
            QCheckBox {{
                color: #444444;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QCheckBox::indicator:checked {{
                image: url("{checked_path}");
            }}
            QCheckBox::indicator:unchecked {{
                image: url("{unchecked_path}");
            }}
        """

    @staticmethod
    def _get_tab_style() -> str:
        """Get tab widget specific styles"""
        return """
            QTabWidget {
                background-color: transparent;
                border: none;
                border-radius: 10px;
            }
            QWidget {
                background-color: transparent;
            }
            QTabWidget::tab-bar {
                alignment: center;
            }
            QTabBar::tab {
                border-radius: 5px;
                width: 120px;
                height: 20px;
                color: black;
                font-size: 16px;
                font-family: inter;
                padding: 2px;
                background-color: #ffffff;
            }
            QTabBar::tab:selected {
                background: black;
                color: white;
            }
            QTabBar::tab:hover {
                background: #787878;
            }
            QTabWidget::pane {
                border: none;
                background-color: transparent;
                border-radius: 10px;
            }
        """


class PlotRenderer:
    """Handles plot rendering and visual updates"""

    def __init__(self, figure, ax, ax_2=None):
        self.figure = figure
        self.ax = ax
        self.ax_2 = ax_2

    def apply_settings(self, settings: Dict[str, Any], data_frame_1: Dict, data_frame_2: Dict):
        """Apply all settings to the plot"""
        try:
            self._apply_title_settings(settings)
            self._apply_label_settings(settings)
            self._apply_legend_settings(settings)
            self._apply_series_settings(settings, data_frame_1, data_frame_2)
            self._apply_grid_settings(settings)
            self._apply_axis_limits(settings)

            self.figure.tight_layout()
            self.figure.canvas.draw()

        except Exception as e:
            logger.error(f"Error applying settings: {e}")

    def _apply_title_settings(self, settings: Dict[str, Any]):
        """Apply title formatting settings"""
        self.ax.title.set_fontname(settings.get('title_font', 'Arial'))
        self.ax.title.set_fontsize(settings.get('title_size', 12))
        self.ax.title.set_color(settings.get('title_color', '#000000'))

    def _apply_label_settings(self, settings: Dict[str, Any]):
        """Apply label formatting settings"""
        label_font = settings.get('label_font', 'Arial')
        label_size = settings.get('label_size', 10)
        label_color = settings.get('label_color', '#000000')

        self.ax.set_xlabel(self.ax.get_xlabel(), fontname=label_font,
                           fontsize=label_size, color=label_color)
        self.ax.set_ylabel(self.ax.get_ylabel(), fontname=label_font,
                           fontsize=label_size, color=label_color)

        if self.ax_2:
            self.ax_2.set_ylabel(self.ax_2.get_ylabel(), fontname=label_font,
                                 fontsize=label_size, color=label_color)

    def _apply_legend_settings(self, settings: Dict[str, Any]):
        """Apply legend formatting settings"""
        legend_font = settings.get('legend_font', 'Arial')
        legend_size = settings.get('legend_size', 10)
        legend_color = settings.get('legend_color', '#000000')

        font_props = FontProperties(family=legend_font, size=legend_size)

        for ax in [self.ax, self.ax_2]:
            if ax and ax.legend_:
                handles, labels = ax.get_legend_handles_labels()
                ax.legend_.remove()

                if handles and labels:
                    if ax == self.ax:
                        legend = ax.legend(handles, labels,
                                           bbox_to_anchor=(0.5, -0.13),
                                           loc='upper center',
                                           ncol=min(5, len(labels)),
                                           columnspacing=1,
                                           handletextpad=0.5,
                                           borderaxespad=0,
                                           facecolor='none',
                                           edgecolor='#446699',
                                           prop=font_props,
                                           bbox_transform=ax.transAxes)
                    else:
                        legend = ax.legend(bbox_to_anchor=(0.5, -0.2),
                                           loc='upper center',
                                           ncol=5,
                                           columnspacing=1,
                                           handletextpad=0.5,
                                           borderaxespad=0,
                                           facecolor='none',
                                           edgecolor='#446699',
                                           prop=font_props,
                                           bbox_transform=ax.transAxes)

                    for text in legend.get_texts():
                        text.set_color(legend_color)

    def _apply_series_settings(self, settings: Dict[str, Any], data_frame_1: Dict, data_frame_2: Dict):
        """Apply data series color and label settings"""
        # Apply to primary axis
        for line in self.ax.get_lines():
            series_name = line.get_label()
            if series_name and series_name in data_frame_1:
                color = settings.get(f'data_series_color_{series_name}', '#008000')
                legend_name = settings.get(f'legend_name_{series_name}', series_name)
                line.set_color(color)
                line.set_label(legend_name)

        # Apply to secondary axis
        if self.ax_2:
            for line in self.ax_2.get_lines():
                series_name = line.get_label()
                if series_name and series_name in data_frame_2:
                    color = settings.get(f'data_series_color_{series_name}', '#008000')
                    legend_name = settings.get(f'legend_name_{series_name}', series_name)
                    line.set_color(color)
                    line.set_label(legend_name)

    def _apply_grid_settings(self, settings: Dict[str, Any]):
        """Apply grid settings"""
        major_visible = settings.get('major_grid_visible', False)
        minor_visible = settings.get('minor_grid_visible', False)
        major_color = settings.get('major_grid_color', '#008000')
        minor_color = settings.get('minor_grid_color', '#008000')

        if major_visible:
            self.ax.grid(which='major', visible=True, color=major_color, linestyle='-', alpha=0.7)
        else:
            self.ax.grid(which='major', visible=False)

        if minor_visible:
            self.ax.minorticks_on()
            self.ax.grid(which='minor', visible=True, color=minor_color, linestyle=':', alpha=0.5)
        else:
            self.ax.grid(which='minor', visible=False)
            self.ax.minorticks_off()

    def _apply_axis_limits(self, settings: Dict[str, Any]):
        """Apply axis limits"""
        if 'x_axis_min' in settings and 'x_axis_max' in settings:
            self.ax.set_xlim(settings['x_axis_min'], settings['x_axis_max'])

        if 'y_axis_min' in settings and 'y_axis_max' in settings:
            self.ax.set_ylim(settings['y_axis_min'], settings['y_axis_max'])

        if self.ax_2 and 'y2_axis_min' in settings and 'y2_axis_max' in settings:
            self.ax_2.set_ylim(settings['y2_axis_min'], settings['y2_axis_max'])


class CustomNavigationToolbar(NavigationToolbar):
    """Custom navigation toolbar with integrated plot settings"""

    def __init__(self, canvas, parent=None, data_frame=None, data_series_1=None, data_series_2=None):
        super().__init__(canvas, parent)

        self.canvas = canvas
        self.parent = parent
        self.data_series_1 = data_series_1
        self.data_series_2 = data_series_2

        # Initialize managers
        self.settings_manager = PlotSettingsManager()
        self.data_frame_1, self.data_frame_2 = self.settings_manager.initialize_data_frames(data_frame)

        # Setup UI
        self.setIconSize(QSize(24, 24))
        self.setup_custom_icons()
        self._add_settings_button()
        self.setStyleSheet(StylesheetManager.get_toolbar_stylesheet())
        try:
            self.line_manager = LineManager(self.canvas, parent=self.parent)
        except Exception:
            self.line_manager = None

        # Initialize managers with proper references
        # Create a mock main window object that provides the necessary interface
        self.mock_main_window = self._create_mock_main_window()

        # User annotation manager
        self.annotation_manager = AnnotationManager(self.mock_main_window)

        # Line drawing manager
        self.line_drawing_manager = LineDrawingManager(self.mock_main_window)
        self.draggable_lines = []  # List to store all draggable lines

        # Now that managers are created, update the mock main window reference
        self.mock_main_window.line_drawing_manager = self.line_drawing_manager

        # Initialize event handlers
        self._setup_event_handlers()

    def _create_mock_main_window(self):
        """Create a mock main window object that provides the necessary interface for managers"""
        class MockMainWindow:
            def __init__(self, toolbar):
                self.toolbar = toolbar
                self.current_canvas = toolbar.canvas
                self.annotation_drag_mode = False
                # Create mock UI object for status updates
                self.ui = MockUI()
                # Line drawing manager will be set after it's created
                self.line_drawing_manager = None

            def mapToGlobal(self, point):
                # Fallback for context menu positioning
                if hasattr(self.toolbar.parent, 'mapToGlobal'):
                    return self.toolbar.parent.mapToGlobal(point)
                return point

            def register_draggable_line(self, line):
                """Register a draggable line for event handling"""
                if hasattr(self.toolbar, 'draggable_lines'):
                    self.toolbar.draggable_lines.append(line)

            def register_draggable_annotation(self, annotation):
                """Register a draggable annotation for event handling"""
                # Enable dragging by default for new annotations
                annotation.connected = True

        class MockUI:
            def __init__(self):
                self.lblLogInfo = MockLabel()

        class MockLabel:
            def __init__(self):
                self.text = ""

            def setText(self, text):
                self.text = text
                # Optional: print status updates for debugging
                # print(f"[STATUS] {text}")

        return MockMainWindow(self)

    def _setup_event_handlers(self):
        """Setup event handlers for line and annotation tools"""
        # Store event handler IDs for potential cleanup
        self.event_handlers = []

        # Add global event logging for debugging (optional)
        if hasattr(self, 'debug_events') and self.debug_events:
            def log_all_events(event):
                if hasattr(event, 'button') and event.button == 1:  # Only log left clicks
                    print(f"[DEBUG] TOOLBAR EVENT: {event.name} at ({getattr(event, 'x', 'N/A')}, {getattr(event, 'y', 'N/A')}) in axes: {getattr(event, 'inaxes', 'N/A')}")
            self.event_handlers.append(self.canvas.mpl_connect('button_press_event', log_all_events))

        # Connect event handlers for annotation and line tools
        self.event_handlers.extend([
            self.canvas.mpl_connect('button_press_event', self.handle_double_click),
            self.canvas.mpl_connect('button_press_event', self.handle_right_click),
            self.canvas.mpl_connect('button_press_event', self.handle_mouse_press_for_drawing),
            self.canvas.mpl_connect('motion_notify_event', self.handle_mouse_motion_for_drawing),
            self.canvas.mpl_connect('button_release_event', self.handle_mouse_release_for_drawing),
            self.canvas.mpl_connect('pick_event', self.handle_line_pick),
            self.canvas.mpl_connect('motion_notify_event', self.handle_line_motion),
            self.canvas.mpl_connect('button_release_event', self.handle_line_release)
        ])

        print(f"[DEBUG] CustomNavigationToolbar: Connected {len(self.event_handlers)} event handlers")

    def cleanup_event_handlers(self):
        """Cleanup event handlers when toolbar is destroyed"""
        if hasattr(self, 'event_handlers'):
            for handler_id in self.event_handlers:
                try:
                    self.canvas.mpl_disconnect(handler_id)
                except:
                    pass  # Handler might already be disconnected
            self.event_handlers.clear()
            print("[DEBUG] CustomNavigationToolbar: Cleaned up event handlers")

    def _add_settings_button(self):
        """Add custom settings button to toolbar"""
        self.addSeparator()

        # Add annotation button
        annotation_btn = QPushButton()
        annotation_btn.setText("✒️")  # Using emoji as icon
        annotation_btn.setToolTip('Add Annotations (Double-click on plot to add)')
        annotation_btn.setCursor(Qt.PointingHandCursor)
        annotation_btn.setCheckable(True)
        annotation_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 5px;
                font-size: 16px;
            }
            QPushButton:checked {
                background-color: #0078d4;
                color: white;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:checked:hover {
                background-color: #106ebe;
            }
        """)
        annotation_btn.clicked.connect(self.toggle_annotation_mode)
        self.addWidget(annotation_btn)
        self.annotation_btn = annotation_btn

        # Add line drawing button
        line_btn = QPushButton()
        line_btn.setText("📏")  # Using ruler emoji as icon
        line_btn.setToolTip(
            'Enhanced Line Tool:\n'
            '• Double-click-drag to create lines\n'
            '• Single-click to select/move lines\n'
            '• Right-click for context menu\n'
            '• Del key to delete selected lines\n'
            '• F key to cycle orientation modes\n'
            '• L key to toggle line mode\n'
            '• Esc to clear selection'
        )
        line_btn.setCursor(Qt.PointingHandCursor)
        line_btn.setCheckable(True)
        line_btn.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 5px;
                font-size: 16px;
            }
            QPushButton:checked {
                background-color: #ff6b35;
                color: white;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:checked:hover {
                background-color: #e55a2b;
            }
        """)
        line_btn.clicked.connect(self.toggle_line_drawing_mode)
        self.addWidget(line_btn)
        self.line_btn = line_btn

        # Add settings button
        settings_btn = QPushButton()
        settings_icon = QIcon()
        settings_icon_path = get_resource_path("assets/advanced_settings.png")
        settings_icon.addFile(settings_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        settings_btn.setIcon(settings_icon)
        settings_btn.setIconSize(QSize(25, 25))
        settings_btn.setToolTip('Plot Settings')
        settings_btn.setCursor(Qt.PointingHandCursor)
        settings_btn.clicked.connect(self.open_plot_settings)
        self.addWidget(settings_btn)

        # Lines menu
        lines_btn = QToolButton()
        lines_btn.setText("Lines")
        lines_btn.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        lmenu = QMenu(lines_btn)
        act_snap = lmenu.addAction("Snap to data points")
        act_snap.setCheckable(True)
        act_settings = lmenu.addAction("Line settings…")
        lmenu.addSeparator()
        act_export = lmenu.addAction("Export lines…")
        act_import = lmenu.addAction("Import lines…")
        lmenu.addSeparator()
        act_clear = lmenu.addAction("Clear lines")
        lines_btn.setMenu(lmenu)
        self.addWidget(lines_btn)

        def _sync_snap():
            if self.line_manager:
                self.line_manager.set_snap(act_snap.isChecked())

        act_snap.toggled.connect(_sync_snap)

        def _export_lines():
            if not self.line_manager: return
            path = QFileDialog.getSaveFileName(self.parent, "Export lines", str(_profiles_path()), "JSON (*.json)")[0]
            if path:
                try:
                    _ensure_parent(Path(path))
                    self.line_manager.export_lines(path)
                except Exception as e:
                    QtWidgets.QMessageBox.warning(self.parent, "Export failed", str(e))

        def _import_lines():
            if not self.line_manager: return
            path = QFileDialog.getOpenFileName(self.parent, "Import lines", str(_profiles_path()), "JSON (*.json)")[0]
            if path:
                try:
                    self.line_manager.import_lines(path)
                except Exception as e:
                    QtWidgets.QMessageBox.warning(self.parent, "Import failed", str(e))

        def _clear_lines():
            if self.line_manager:
                self.line_manager.clear()

        def _show_line_settings():
            if self.line_manager:
                self.line_manager.show_settings_dialog()
            else:
                # Fallback to main window line manager
                main_window = self.parent
                while main_window and not hasattr(main_window, 'line_drawing_manager'):
                    main_window = main_window.parent()
                if main_window and hasattr(main_window, 'line_drawing_manager'):
                    main_window.line_drawing_manager.show_settings_dialog()

        act_export.triggered.connect(_export_lines)
        act_import.triggered.connect(_import_lines)
        act_clear.triggered.connect(_clear_lines)
        act_settings.triggered.connect(_show_line_settings)

        # Add help action
        act_help = lmenu.addAction("Help...")

        def _show_line_help():
            from PySide6.QtWidgets import QMessageBox
            help_text = """
Enhanced Line Tool Help

CREATING LINES:
• Double-click and drag to create a new line
• Use F key to cycle between orientation modes:
  - Free: Lines can be drawn in any direction
  - Horizontal: Lines are constrained to horizontal
  - Vertical: Lines are constrained to vertical

SELECTING AND MOVING LINES:
• Single-click on a line to select it
• Drag selected lines to move them
• Ctrl+A to select all lines
• Esc to clear selection

LINE OPERATIONS:
• Right-click on a line for context menu
• Del key to delete selected lines
• Access line properties through context menu

KEYBOARD SHORTCUTS:
• L - Toggle line drawing mode
• F - Cycle orientation modes (Free → Horizontal → Vertical)
• Del - Delete selected lines
• Esc - Clear selection and cancel drawing
• Ctrl+A - Select all lines

SETTINGS:
• Use "Line settings..." to configure default properties
• Settings are automatically saved for new lines
• Snap-to-grid option available in settings
            """
            QMessageBox.information(self.parent, "Line Tool Help", help_text.strip())

        act_help.triggered.connect(_show_line_help)

    def setup_custom_icons(self):
        """Replace default matplotlib toolbar icons with custom ones"""
        icon_mapping = {
            'home': 'home.png',
            'back': 'back.png',
            'forward': 'forward.png',
            'pan': 'pan.png',
            'zoom': 'zoom.png',
            'save': 'save.png',
            'subplots': 'subplots.png'
        }

        for action in self.actions():
            try:
                action_name = action.text().lower() if action.text() else ""
                if not action_name or action_name == "separator":
                    continue

                for key, icon_name in icon_mapping.items():
                    if key in action_name:
                        icon_path = get_resource_path(f"assets/{icon_name}")
                        if os.path.exists(icon_path):
                            action.setIcon(QIcon(icon_path))
                        break
            except Exception as e:
                logger.warning(f"Error setting custom icon for action {action_name}: {e}")

    def edit_parameters(self):
        """Override edit_parameters with custom styling"""
        super().edit_parameters()
        dialog = self._get_active_dialog()
        if dialog:
            dialog.setStyleSheet(StylesheetManager.get_dialog_stylesheet())

    def configure_subplots(self):
        """Override configure_subplots with custom styling"""
        super().configure_subplots()
        dialog = self._get_active_dialog()
        if dialog:
            dialog.setStyleSheet(StylesheetManager.get_dialog_stylesheet())

    def _get_active_dialog(self):
        """Get the currently active dialog window"""
        try:
            for widget in QtWidgets.QApplication.topLevelWidgets():
                if isinstance(widget, QtWidgets.QDialog) and widget.isVisible():
                    return widget
        except Exception as e:
            logger.warning(f"Error finding active dialog: {e}")
        return None

    def toggle_annotation_mode(self):
        """Toggle annotation creation mode"""
        try:
            is_enabled = self.annotation_btn.isChecked()

            # Use our own annotation manager
            self.annotation_manager.set_annotation_mode(is_enabled)

            # Disable line drawing mode if annotation mode is enabled
            if is_enabled and hasattr(self, 'line_btn'):
                self.line_btn.setChecked(False)
                self.line_drawing_manager.set_drawing_mode(False)

            if is_enabled:
                self.annotation_btn.setToolTip('Annotation mode ON - Double-click to add annotations')
                print("[DEBUG] Annotation mode enabled")
            else:
                self.annotation_btn.setToolTip('Add Annotations (Double-click on plot to add)')
                print("[DEBUG] Annotation mode disabled")

        except Exception as e:
            logger.error(f"Error toggling annotation mode: {e}")
            self.annotation_btn.setChecked(False)

    def toggle_line_drawing_mode(self):
        """Toggle line drawing mode"""
        try:
            is_enabled = self.line_btn.isChecked()

            # Use our own line drawing manager
            self.line_drawing_manager.set_drawing_mode(is_enabled)

            # Disable annotation mode if line drawing mode is enabled
            if is_enabled and hasattr(self, 'annotation_btn'):
                self.annotation_btn.setChecked(False)
                self.annotation_manager.set_annotation_mode(False)

            if is_enabled:
                self.line_btn.setToolTip(
                    'Enhanced Line Tool ACTIVE:\n'
                    '• Double-click-drag to create lines\n'
                    '• Single-click to select/move lines\n'
                    '• Right-click for context menu\n'
                    '• Del key to delete selected lines\n'
                    '• F key to cycle orientation modes\n'
                    '• L key to toggle line mode\n'
                    '• Esc to clear selection'
                )
                orientation = self.line_drawing_manager.get_line_orientation()
                print(f"[DEBUG] Line Tool Active - Mode: {orientation.title()}")
            else:
                self.line_btn.setToolTip(
                    'Enhanced Line Tool:\n'
                    '• Double-click-drag to create lines\n'
                    '• Single-click to select/move lines\n'
                    '• Right-click for context menu\n'
                    '• Del key to delete selected lines\n'
                    '• F key to cycle orientation modes\n'
                    '• L key to toggle line mode\n'
                    '• Esc to clear selection'
                )
                print("[DEBUG] Line Tool Disabled")

        except Exception as e:
            logger.error(f"Error toggling line drawing: {e}")
            self.line_btn.setChecked(False)


    def open_plot_settings(self):
        """Show the plot settings dialog"""
        try:
            self.settings_dialog = PlotSettingsDialog(
                self.parent,
                self.canvas.figure,
                {'data_frame_1': self.data_frame_1, 'data_frame_2': self.data_frame_2},
                self.settings_manager.settings,
                data_series_1=self.data_series_1,
                data_series_2=self.data_series_2
            )
            self.settings_dialog.show()
        except Exception as e:
            logger.error(f"Error opening plot settings dialog: {e}")

    def show_annotation_edit_dialog(self, draggable_annotation):
        """Show the annotation edit dialog"""
        try:
            dialog = AnnotationEditDialog(draggable_annotation, self)
            result = dialog.exec()

            if result == 2:  # Delete was clicked
                self.annotation_manager.delete_annotation(draggable_annotation)
            elif result == QDialog.Accepted:
                # Changes were already applied in the dialog
                pass

        except Exception as e:
            print(f"[ERROR] Failed to show annotation edit dialog: {e}")

    def handle_double_click(self, event):
        """Handle double-click events for adding annotations"""
        if (event.inaxes and
            self.annotation_manager.is_annotation_mode_enabled() and
            event.dblclick):

            # Create new annotation at click position
            x, y = event.xdata, event.ydata
            if x is not None and y is not None:
                draggable_annotation = self.annotation_manager.create_annotation(
                    event.inaxes, x, y, "New Annotation"
                )
                if draggable_annotation:
                    # Immediately open edit dialog
                    self.show_annotation_edit_dialog(draggable_annotation)

    def handle_right_click(self, event):
        """Handle right-click events for annotation and line context menus"""
        if event.inaxes and event.button == 3:  # Right click
            try:
                # First check if we clicked on a line
                clicked_line = self.line_drawing_manager.find_line_at_point((event.xdata, event.ydata))
                if clicked_line:
                    print(f"[DEBUG] Right-clicked on line: {clicked_line.line_id}")
                    self.show_line_context_menu(clicked_line, event)
                    return

                # Then check if we clicked on a user annotation
                clicked_annotation = None
                min_distance = float('inf')

                for annotation_key, draggable_annotation in self.annotation_manager.user_annotations.items():
                    # Check if the click is within the annotation bounds
                    contains, info = draggable_annotation.annotation.contains(event)
                    if contains:
                        # If multiple annotations overlap, choose the closest one
                        if draggable_annotation.use_xytext:
                            ann_pos = draggable_annotation.annotation.xytext
                        else:
                            ann_pos = draggable_annotation.annotation.get_position()

                        distance = ((event.xdata - ann_pos[0])**2 + (event.ydata - ann_pos[1])**2)**0.5
                        if distance < min_distance:
                            min_distance = distance
                            clicked_annotation = draggable_annotation

                if clicked_annotation:
                    print(f"[DEBUG] Right-clicked on annotation: {clicked_annotation.position_key}")
                    # Show context menu for this annotation
                    self.show_annotation_context_menu(clicked_annotation, event)

            except Exception as e:
                print(f"[ERROR] Error in right-click handler: {e}")

    def handle_mouse_press_for_drawing(self, event):
        """Handle mouse press for line drawing"""
        if event.inaxes and self.line_drawing_manager.is_drawing_mode_enabled():
            x, y = event.xdata, event.ydata
            if x is not None and y is not None:

                if event.button == 1:  # Left click
                    # Check for double-click
                    if hasattr(event, 'dblclick') and event.dblclick:
                        # For double-click, use strict mode to avoid detecting nearby lines
                        nearby_line = self.line_drawing_manager.find_line_at_point((x, y), strict_mode=True)
                        if nearby_line:
                            print(f"[DEBUG] Double-click detected near line {nearby_line.line_id}, but creating new line anyway")
                        # Always create new line for double-click, regardless of proximity
                        self.line_drawing_manager.start_drawing(event.inaxes, (x, y))
                    else:
                        # Single click - check if clicking on existing line for movement (use normal tolerance)
                        line = self.line_drawing_manager.find_line_at_point((x, y))
                        if line:
                            # Clear other selections first
                            self.line_drawing_manager.clear_selection()
                            # Select and start dragging this line
                            self.line_drawing_manager.select_line(line)
                            line.handle_pick(event)

                elif event.button == 3:  # Right click
                    # Check if right-clicking on existing line for context menu
                    line = self.line_drawing_manager.find_line_at_point((x, y))
                    if line:
                        # Select the line and show context menu
                        self.line_drawing_manager.clear_selection()
                        self.line_drawing_manager.select_line(line)
                        line.show_context_menu(event)

    def handle_mouse_motion_for_drawing(self, event):
        """Handle mouse motion for line drawing"""
        if (event.inaxes and
            self.line_drawing_manager.drawing_start_point is not None):

            x, y = event.xdata, event.ydata
            if x is not None and y is not None:
                self.line_drawing_manager.update_drawing((x, y))

    def handle_mouse_release_for_drawing(self, event):
        """Handle mouse release for line drawing"""
        if (event.inaxes and
            self.line_drawing_manager.drawing_start_point is not None and
            event.button == 1):  # Left click release

            x, y = event.xdata, event.ydata
            if x is not None and y is not None:
                line = self.line_drawing_manager.finish_drawing((x, y))
                if line:
                    # Immediately open style dialog for new line
                    self.show_line_style_dialog(line)

    def handle_line_pick(self, event):
        """Centralized pick event handler for lines"""
        for line in self.draggable_lines:
            if line.handle_pick(event):
                break  # Only one line should handle the event

    def handle_line_motion(self, event):
        """Centralized motion event handler for lines"""
        for line in self.draggable_lines:
            if line.handle_motion(event):
                break  # Only the dragging line should handle motion

    def handle_line_release(self, event):
        """Centralized release event handler for lines"""
        for line in self.draggable_lines:
            if line.handle_release(event):
                break  # Only the dragging line should handle release

    def show_line_context_menu(self, draggable_line, event):
        """Show context menu for line/arrow"""
        from PySide6.QtWidgets import QMenu
        from PySide6.QtGui import QCursor

        menu = QMenu(self.parent)
        menu.setStyleSheet("""
            QMenu {
                background-color: #2b2b2b;
                color: white;
                border: 1px solid #555;
                border-radius: 5px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 3px;
            }
            QMenu::item:selected {
                background-color: #ff6b35;
            }
        """)

        edit_action = menu.addAction("Edit Line Style")
        edit_action.triggered.connect(lambda: self.show_line_style_dialog(draggable_line))

        # Add line type conversion options
        if draggable_line.line_type == 'arrow':
            convert_action = menu.addAction("Convert to Line")
            convert_action.triggered.connect(lambda: draggable_line.set_line_type('line'))
        else:
            convert_action = menu.addAction("Convert to Arrow")
            convert_action.triggered.connect(lambda: draggable_line.set_line_type('arrow'))

        menu.addSeparator()

        delete_action = menu.addAction("Delete Line")
        delete_action.triggered.connect(lambda: self.line_drawing_manager.delete_line(draggable_line))

        # Show context menu at cursor position
        try:
            cursor_pos = QCursor.pos()
            menu.exec(cursor_pos)
        except Exception as e:
            print(f"[ERROR] Failed to show line context menu: {e}")

    def show_line_style_dialog(self, draggable_line):
        """Show the line style dialog"""
        try:
            self.line_drawing_manager.show_settings_dialog(draggable_line)
        except Exception as e:
            print(f"[ERROR] Failed to show line style dialog: {e}")

    def show_annotation_context_menu(self, draggable_annotation, event):
        """Show context menu for annotation"""
        from PySide6.QtWidgets import QMenu
        from PySide6.QtGui import QCursor

        menu = QMenu(self.parent)
        menu.setStyleSheet("""
            QMenu {
                background-color: #2b2b2b;
                color: white;
                border: 1px solid #555;
                border-radius: 5px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 3px;
            }
            QMenu::item:selected {
                background-color: #0078d4;
            }
        """)

        edit_action = menu.addAction("Edit Annotation")
        edit_action.triggered.connect(lambda: self.show_annotation_edit_dialog(draggable_annotation))

        delete_action = menu.addAction("Delete Annotation")
        delete_action.triggered.connect(lambda: self.annotation_manager.delete_annotation(draggable_annotation))

        # Show context menu at cursor position (most reliable method)
        try:
            # Get current cursor position - this is the most reliable approach
            cursor_pos = QCursor.pos()
            print(f"[DEBUG] Showing context menu at cursor position: ({cursor_pos.x()}, {cursor_pos.y()})")
            menu.exec(cursor_pos)
        except Exception as e:
            print(f"[ERROR] Failed to show context menu: {e}")


class PlotSettingsDialog(QDialog, Ui_Plot_settings_Dialog):
    """Plot settings dialog with centralized management"""

    def __init__(self, parent, figure, data_frame, settings, data_series_1=None, data_series_2=None):
        super().__init__(parent)
        self.setupUi(self)

        self.figure = figure
        self.data_series_1 = data_series_1
        self.data_series_2 = data_series_2

        # Initialize managers
        self.settings_manager = PlotSettingsManager()
        self.data_frame_1, self.data_frame_2 = self.settings_manager.initialize_data_frames(data_frame)

        # Debug: Print data frame information
        print(f"Debug: PlotSettingsDialog initialized with data_frame type: {type(data_frame)}")
        if isinstance(data_frame, dict):
            print(f"Debug: Original data_frame keys: {list(data_frame.keys())}")
            for key, value in data_frame.items():
                print(f"Debug: data_frame['{key}'] type: {type(value)}")
                if isinstance(value, dict):
                    print(f"Debug: data_frame['{key}'] keys: {list(value.keys())}")
        print(f"Debug: data_frame_1 type: {type(self.data_frame_1)}, keys: {list(self.data_frame_1.keys()) if self.data_frame_1 else 'None'}")
        print(f"Debug: data_frame_2 type: {type(self.data_frame_2)}, keys: {list(self.data_frame_2.keys()) if self.data_frame_2 else 'None'}")

        # Also check what's in the plot lines for comparison
        if hasattr(self, 'ax') and self.ax and self.ax.get_lines():
            print(f"Debug: Plot has {len(self.ax.get_lines())} lines")
            for i, line in enumerate(self.ax.get_lines()):
                print(f"Debug: Line {i}: label='{line.get_label()}', data points={len(line.get_xdata())}")
        else:
            print("Debug: No plot lines found or axes not available")

        # Initialize axes
        self.ax = None
        self.ax_2 = None
        self._initialize_axes()

        # Initialize renderer
        self.renderer = PlotRenderer(self.figure, self.ax, self.ax_2)

        # Initialize settings
        self.settings_manager.settings = settings or {}

        # Setup UI
        self._setup_ui()
        self._connect_signals()
        self._initialize_values()
        self.setup_icons()

        """Initialize data frames from input parameter"""
        exponential_icon = QIcon(get_resource_path("assets/exponential.png"))
        self.pushButton.setIcon(exponential_icon)
        self.pushButton.setIconSize(QSize(58, 58))

        linear_icon = QIcon(get_resource_path("assets/linear.png"))
        self.pushButton_2.setIcon(linear_icon)
        self.pushButton_2.setIconSize(QSize(58, 58))

        logarithmic_icon = QIcon(get_resource_path("assets/logarithmic.png"))
        self.pushButton_3.setIcon(logarithmic_icon)
        self.pushButton_3.setIconSize(QSize(58, 58))

        polynomial_icon = QIcon(get_resource_path("assets/polynomial.png"))
        self.pushButton_4.setIcon(polynomial_icon)
        self.pushButton_4.setIconSize(QSize(58, 58))

        power_icon = QIcon(get_resource_path("assets/power.png"))
        self.pushButton_5.setIcon(power_icon)
        self.pushButton_5.setIconSize(QSize(58, 58))

    def _initialize_axes(self):
        """Safely initialize axes from figure"""
        try:
            if hasattr(self, 'figure') and self.figure is not None:
                if self.figure.axes:
                    self.ax = self.figure.axes[0]
                    if len(self.figure.axes) > 1:
                        self.ax_2 = self.figure.axes[1]
                    logger.debug(f"Initialized axes: ax={self.ax}, ax_2={self.ax_2}")
                else:
                    # Create axes if none exist
                    self.ax = self.figure.gca()
                    logger.debug(f"Created new axes: ax={self.ax}")
            else:
                logger.error("Figure not available for axes initialization")
                self.ax = None
                self.ax_2 = None
        except (IndexError, AttributeError) as e:
            logger.warning(f"Error accessing figure axes: {e}")
            try:
                self.ax = self.figure.gca() if hasattr(self, 'figure') and self.figure else None
            except:
                self.ax = None
            self.ax_2 = None

    def _setup_ui(self):
        """Setup UI components"""
        # Font size options
        font_sizes = [str(i) for i in range(8, 25)]
        for combo in [self.comboBoxTitleFontSize, self.comboBoxLabelFontSize, self.comboBoxLegendFontSize]:
            combo.addItems(font_sizes)

        # Set cursor for color buttons
        color_buttons = [
            self.btnLabelFontColorPallet, self.btnTitleFontColorPallet,
            self.btnLegendFontColorPallet, self.btnDataSelectionColorPallet,
            self.btnMajorGridColorPallet, self.btnMinorGridColorPallet
        ]
        for button in color_buttons:
            button.setCursor(Qt.PointingHandCursor)

    def _connect_signals(self):
        """Connect all UI signals"""
        # Dialog buttons
        self.pushButton_8.clicked.connect(self.accept_changes)
        self.pushButton_7.clicked.connect(self.reject)

        # Color picker buttons
        color_connections = [
            (self.btnTitleFontColorPallet, 'title_color', self.lnEdtTitleFontColorHex),
            (self.btnLabelFontColorPallet, 'label_color', self.lnEdtLabelFontColorHex),
            (self.btnLegendFontColorPallet, 'legend_color', self.lnEdtLegendFontColorHex),
            (self.btnDataSelectionColorPallet, 'data_series_color', self.lnEdtDataSelectionColorHex),
            (self.btnMajorGridColorPallet, 'major_grid_color', self.lnEdtMajorGridColorHex),
            (self.btnMinorGridColorPallet, 'minor_grid_color', self.lnEdtMinorGridColorHex),
        ]

        for button, setting_key, line_edit in color_connections:
            button.clicked.connect(lambda checked, b=button, k=setting_key, le=line_edit:
                                   self._pick_color(b, k, le))

        # Other connections
        self.lnEdtTitleFontColorHex.editingFinished.connect(
            lambda: self._dynamic_color_change(self.lnEdtTitleFontColorHex.text()))
        self.lnEdtLegendName.editingFinished.connect(self._update_legend_name)
        self.comboBoxDataSelection.currentTextChanged.connect(self._update_data_series_ui)
        self.comboBoxDataSelectionForPlotRange.currentTextChanged.connect(self._update_range_spinboxes)
        self.customDoubleSpinBoxRangeMin.valueChanged.connect(self._update_plot_range)
        self.customDoubleSpinBoxRangeMax.valueChanged.connect(self._update_plot_range)

        # Trendline connections - pass checked state to determine if adding or removing
        self.radioButton.toggled.connect(lambda checked: self.add_trendline('exponential') if checked else None)
        self.radioButton_2.toggled.connect(lambda checked: self.add_trendline('linear') if checked else None)
        self.radioButton_5.toggled.connect(lambda checked: self.add_trendline('logarithmic') if checked else None)
        self.radioButton_6.toggled.connect(lambda checked: self.add_trendline('polynomial') if checked else None)
        self.radioButton_7.toggled.connect(lambda checked: self.add_trendline('power') if checked else None)
        self.radioButton_8.toggled.connect(lambda checked: self.add_trendline('moving_average') if checked else None)

    def _initialize_values(self):
        """Initialize dialog with current plot values"""
        if not self.ax:
            logger.error("No axes available for initialization")
            return

        # Extract current settings from plot
        current_settings = self.settings_manager.extract_plot_settings(self.ax, self.ax_2)
        self.settings_manager.settings.update(current_settings)

        print(f"The current settings are: {self.settings_manager.settings}")

        # Initialize UI elements
        self._populate_data_selection_combos()
        self._set_font_settings()
        self._set_color_settings()
        self._set_grid_settings()
        self._set_range_settings()
        self._setup_dual_axis_toggle()

        # Setup trendline controls with retry mechanism
        if hasattr(self, 'ax') and self.ax is not None:
            self._setup_trendline_controls()
        else:
            # Delay setup until axes are available
            logger.warning("Axes not ready, will setup trendline controls later")
            QTimer.singleShot(100, self._delayed_trendline_setup)

    def _delayed_trendline_setup(self):
        """Delayed setup of trendline controls when axes become available"""
        try:
            if hasattr(self, 'ax') and self.ax is not None:
                logger.debug("Setting up trendline controls (delayed)")
                self._setup_trendline_controls()
            else:
                logger.warning("Axes still not available, skipping trendline controls")
        except Exception as e:
            logger.error(f"Error in delayed trendline setup: {e}")

    def get_extrapolation_limits(self):
        """Get extrapolation limits for trendline with proper error handling"""
        try:
            forward = float(self.extrapolate_forward.text()) if self.extrapolate_forward.text() else 0
            backward = float(self.extrapolate_backward.text()) if self.extrapolate_backward.text() else 0
            return forward, backward
        except ValueError:
            logger.warning("Invalid extrapolation values, using defaults")
            return 0, 0

    def _populate_data_selection_combos(self):
        """Populate data selection combo boxes"""
        self.comboBoxDataSelection.clear()
        self.comboBoxDataSelectionForPlotRange.clear()

        # Add data series
        if self.data_frame_1:
            for series in self.data_frame_1.keys():
                legend_name = self.settings_manager.settings.get(f'legend_name_{series}', series)
                self.comboBoxDataSelection.addItem(legend_name)

        # Add axis names for range selection
        self.comboBoxDataSelectionForPlotRange.addItem("X-Axis")
        self.comboBoxDataSelectionForPlotRange.addItem("Y-Axis")
        if self.ax_2:
            self.comboBoxDataSelectionForPlotRange.addItem("Y-Axis 2")

    def _set_font_settings(self):
        """Set font-related UI elements"""
        settings = self.settings_manager.settings

        # Title font
        font = QtGui.QFont()
        font.setFamily(settings.get('title_font', 'Arial'))
        self.comboBoxTitleFont.setCurrentFont(font)
        self.comboBoxTitleFontSize.setCurrentText(str(settings.get('title_size', 12)))

        # Label font
        font.setFamily(settings.get('label_font', 'Arial'))
        self.comboBoxLabelFont.setCurrentFont(font)
        self.comboBoxLabelFontSize.setCurrentText(str(settings.get('label_size', 10)))

        # Legend font
        font.setFamily(settings.get('legend_font', 'Arial'))
        self.comboBoxLegendFont.setCurrentFont(font)
        self.comboBoxLegendFontSize.setCurrentText(str(settings.get('legend_size', 10)))

    def _set_color_settings(self):
        """Set color-related UI elements"""
        settings = self.settings_manager.settings

        # Title color
        title_color = settings.get('title_color', '#000000')
        self.lnEdtTitleFontColorHex.setText(title_color)
        self.btnTitleFontColorPallet.setStyleSheet(f"background-color: {title_color};")

        # Label color
        label_color = settings.get('label_color', '#000000')
        self.lnEdtLabelFontColorHex.setText(label_color)
        self.btnLabelFontColorPallet.setStyleSheet(f"background-color: {label_color};")

        # Legend color
        legend_color = settings.get('legend_color', '#000000')
        self.lnEdtLegendFontColorHex.setText(legend_color)
        self.btnLegendFontColorPallet.setStyleSheet(f"background-color: {legend_color};")

        # Data series color (first series)
        if self.data_frame_1:
            first_series = list(self.data_frame_1.keys())[0]
            series_color = settings.get(f'data_series_color_{first_series}', '#008000')
            legend_name = settings.get(f'legend_name_{first_series}', first_series)
            self.lnEdtDataSelectionColorHex.setText(series_color)
            self.btnDataSelectionColorPallet.setStyleSheet(f"background-color: {series_color};")
            self.lnEdtLegendName.setText(legend_name)

        # Grid colors
        major_grid_color = settings.get('major_grid_color', '#008000')
        minor_grid_color = settings.get('minor_grid_color', '#008000')
        self.lnEdtMajorGridColorHex.setText(major_grid_color)
        self.lnEdtMinorGridColorHex.setText(minor_grid_color)
        self.btnMajorGridColorPallet.setStyleSheet(f"background-color: {major_grid_color};")
        self.btnMinorGridColorPallet.setStyleSheet(f"background-color: {minor_grid_color};")

    def _set_grid_settings(self):
        """Set grid-related UI elements"""
        settings = self.settings_manager.settings
        print(f"The Current settings at grid are: {settings}")
        self.checkBoxMinorGrid.setChecked(settings['minor_grid_visible'])
        print(f"The minor grid visibility is: {settings['minor_grid_visible']}")
        print(f"The Current settings at major grid are: {settings}")
        self.checkBoxMajorGrid.setChecked(settings['major_grid_visible'])
        print(f"The major grid visibility is: {settings['major_grid_visible']}")

    def _set_range_settings(self):
        """Set range-related UI elements"""
        settings = self.settings_manager.settings
        if 'x_axis_min' in settings and 'x_axis_max' in settings:
            self.customDoubleSpinBoxRangeMin.setValue(settings['x_axis_min'])
            self.customDoubleSpinBoxRangeMax.setValue(settings['x_axis_max'])

    def _setup_dual_axis_toggle(self):
        """Setup toggle switch for dual-axis plots"""
        if self.ax_2 and self.data_series_1 and self.data_series_2:
            font = QFont("Arial", 12)
            metrics = QFontMetricsF(font)
            width_1 = metrics.horizontalAdvance(self.data_series_1)
            width_2 = metrics.horizontalAdvance(self.data_series_2)
            max_width = int(max(width_1, width_2)) + 20

            self.data_selection_toggle = MultiStateToggleSwitch(
                [self.data_series_1, self.data_series_2],
                width=max_width,
                height=25
            )
            self.dataSettingsHeaderFrame.layout().addWidget(self.data_selection_toggle)
            self.data_selection_toggle.modeChanged.connect(self._handle_data_frame_change)

    def _setup_trendline_controls(self):
        """Setup trendline data range selection controls"""
        try:
            # Check if axes are available
            if not hasattr(self, 'ax') or self.ax is None:
                logger.warning("Axes not available, skipping trendline controls setup")
                return
            # Create trendline data range controls dynamically
            from PySide6.QtWidgets import QLabel, QComboBox, QHBoxLayout, QFrame
            from gui.custom_double_spin_box import CustomDoubleSpinBox

            # Create frame for trendline range controls
            self.trendline_range_frame = QFrame()
            self.trendline_range_frame.setFrameShape(QFrame.Shape.StyledPanel)
            self.trendline_range_frame.setFrameShadow(QFrame.Shadow.Raised)

            # Create layout
            layout = QtWidgets.QVBoxLayout(self.trendline_range_frame)

            # Add label
            range_label = QLabel("Trendline Data Range Settings")
            range_label.setStyleSheet("""
                color: #F9F9F9;
                font-family: inter;
                font-weight: bold;
                font-size: 16px;
            """)
            layout.addWidget(range_label)

            # Create horizontal layout for controls
            controls_layout = QHBoxLayout()

            # Data series selection for trendline
            self.comboBoxTrendlineDataSeries = QComboBox()
            self.comboBoxTrendlineDataSeries.setMinimumSize(QSize(0, 45))
            self.comboBoxTrendlineDataSeries.setStyleSheet("""
                QComboBox {
                    border: 1px solid #303030;
                    border-radius: 10px;
                    padding: 4px 15px;
                    background: #1C1C1C;
                    color: #EEEEEE;
                    font-size: 16px;
                }
            """)
            controls_layout.addWidget(self.comboBoxTrendlineDataSeries)

            # Range min spinbox
            self.trendlineRangeMin = CustomDoubleSpinBox()
            self.trendlineRangeMin.setMinimumSize(QSize(100, 45))
            self.trendlineRangeMin.setMaximumSize(QSize(130, 16777215))
            self.trendlineRangeMin.setStyleSheet("""
                QDoubleSpinBox {
                    border: 1px solid #303030;
                    border-radius: 10px;
                    padding: 4px 15px;
                    background: #1C1C1C;
                    color: #EEEEEE;
                    font-size: 16px;
                }
            """)
            self.trendlineRangeMin.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.trendlineRangeMin.setButtonSymbols(QtWidgets.QAbstractSpinBox.ButtonSymbols.NoButtons)
            self.trendlineRangeMin.setMinimum(-10000000.0)
            self.trendlineRangeMin.setMaximum(10000000.0)
            self.trendlineRangeMin.setDecimals(6)
            self.trendlineRangeMin.setSingleStep(0.1)
            # Install event filter for better text selection behavior
            self.trendlineRangeMin.installEventFilter(self)
            controls_layout.addWidget(self.trendlineRangeMin)

            # Range max spinbox
            self.trendlineRangeMax = CustomDoubleSpinBox()
            self.trendlineRangeMax.setMinimumSize(QSize(100, 45))
            self.trendlineRangeMax.setMaximumSize(QSize(130, 16777215))
            self.trendlineRangeMax.setStyleSheet("""
                QDoubleSpinBox {
                    border: 1px solid #303030;
                    border-radius: 10px;
                    padding: 4px 15px;
                    background: #1C1C1C;
                    color: #EEEEEE;
                    font-size: 16px;
                }
            """)
            self.trendlineRangeMax.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.trendlineRangeMax.setButtonSymbols(QtWidgets.QAbstractSpinBox.ButtonSymbols.NoButtons)
            self.trendlineRangeMax.setMinimum(-10000000.0)
            self.trendlineRangeMax.setMaximum(10000000.0)
            self.trendlineRangeMax.setDecimals(6)
            self.trendlineRangeMax.setSingleStep(0.1)
            # Install event filter for better text selection behavior
            self.trendlineRangeMax.installEventFilter(self)
            controls_layout.addWidget(self.trendlineRangeMax)

            layout.addLayout(controls_layout)

            # Add the frame to the analysis tab after the trendline selection frame
            analysis_layout = self.analysisTab.layout()
            if analysis_layout:
                # Find the trendline selection frame and add our controls after it
                for i in range(analysis_layout.count()):
                    item = analysis_layout.itemAt(i)
                    if item and item.widget() and hasattr(item.widget(), 'objectName'):
                        if item.widget().objectName() == 'trendline_selection_frame':
                            analysis_layout.insertWidget(i + 1, self.trendline_range_frame)
                            break
                else:
                    # If not found, add at the end
                    analysis_layout.addWidget(self.trendline_range_frame)

            # Populate data series combo
            self._populate_trendline_data_series()

            # Connect signals
            self.comboBoxTrendlineDataSeries.currentTextChanged.connect(self._update_trendline_range)
            self._connect_range_signals()

        except Exception as e:
            logger.error(f"Error setting up trendline controls: {e}")

    def eventFilter(self, obj, event):
        """Event filter to handle focus events for spinboxes"""
        try:
            # Handle focus in events for trendline range spinboxes
            if (obj in [getattr(self, 'trendlineRangeMin', None), getattr(self, 'trendlineRangeMax', None)] and
                event.type() == QEvent.Type.FocusIn):
                # Select all text when the spinbox gains focus
                QTimer.singleShot(0, obj.selectAll)
                return False

            # Handle mouse press events for better text selection
            if (obj in [getattr(self, 'trendlineRangeMin', None), getattr(self, 'trendlineRangeMax', None)] and
                event.type() == QEvent.Type.MouseButtonPress):
                # If the spinbox doesn't have focus, give it focus and select all
                if not obj.hasFocus():
                    obj.setFocus()
                    QTimer.singleShot(0, obj.selectAll)
                    return False

        except Exception as e:
            logger.error(f"Error in event filter: {e}")

        return super().eventFilter(obj, event)

    def _populate_trendline_data_series(self):
        """Populate trendline data series combo box"""
        try:
            self.comboBoxTrendlineDataSeries.clear()

            # Helper function to check if data is suitable for trendlines
            def is_suitable_for_trendline(data):
                try:
                    # Handle None or empty data
                    if data is None:
                        return False

                    # Check for scalar data
                    if np.isscalar(data):
                        return False  # Scalar data can't create meaningful trendlines

                    # Check different data types
                    if isinstance(data, (list, tuple)):
                        return len(data) > 1  # Need at least 2 points
                    elif isinstance(data, np.ndarray):
                        return data.size > 1  # Use size for numpy arrays
                    elif hasattr(data, 'values'):  # pandas Series/DataFrame
                        values = data.values
                        return hasattr(values, '__len__') and len(values) > 1
                    elif hasattr(data, '__len__') and not isinstance(data, (str, bytes)):
                        return len(data) > 1
                    else:
                        # Try to convert to array and check
                        try:
                            arr = np.asarray(data)
                            return arr.size > 1 and not np.isscalar(arr)
                        except:
                            return False
                except Exception as e:
                    print(f"Debug: Error checking data suitability for {type(data)}: {e}")
                    return False

            # Add data series from both data frames
            total_series_found = 0
            suitable_series_found = 0

            if self.data_frame_1:
                print(f"Debug: data_frame_1 has {len(self.data_frame_1)} series: {list(self.data_frame_1.keys())}")
                for series in self.data_frame_1.keys():
                    total_series_found += 1
                    data = self.data_frame_1[series]
                    print(f"Debug: Checking series '{series}' - type: {type(data)}, suitable: {is_suitable_for_trendline(data)}")
                    if is_suitable_for_trendline(data):
                        suitable_series_found += 1
                        legend_name = self.settings_manager.settings.get(f'legend_name_{series}', series)
                        self.comboBoxTrendlineDataSeries.addItem(f"Primary: {legend_name}", userData={'frame': 1, 'series': series})

            if self.data_frame_2:
                print(f"Debug: data_frame_2 has {len(self.data_frame_2)} series: {list(self.data_frame_2.keys())}")
                for series in self.data_frame_2.keys():
                    total_series_found += 1
                    data = self.data_frame_2[series]
                    print(f"Debug: Checking series '{series}' - type: {type(data)}, suitable: {is_suitable_for_trendline(data)}")
                    if is_suitable_for_trendline(data):
                        suitable_series_found += 1
                        legend_name = self.settings_manager.settings.get(f'legend_name_{series}', series)
                        self.comboBoxTrendlineDataSeries.addItem(f"Secondary: {legend_name}", userData={'frame': 2, 'series': series})

            print(f"Debug: Found {total_series_found} total series, {suitable_series_found} suitable for trendlines")

            # If no suitable data series found, try to extract from plot lines
            if suitable_series_found == 0 and hasattr(self, 'ax') and self.ax and self.ax.get_lines():
                print("Debug: No data series found in data frames, trying to extract from plot lines")
                self._extract_series_from_plot()

            # Set initial range values or add fallback options
            if self.comboBoxTrendlineDataSeries.count() > 0:
                self._update_trendline_range()
            else:
                # If no suitable data series found, add fallback options
                print("Debug: No suitable data series found, adding fallback options")

                # Add option to use first line from plot (original behavior)
                if hasattr(self, 'ax') and self.ax and self.ax.get_lines():
                    self.comboBoxTrendlineDataSeries.addItem("Use First Plot Line (Auto)", userData={'frame': 'auto', 'series': 'auto'})
                    print("Debug: Added 'Use First Plot Line' option")

                # If still no options, show message
                if self.comboBoxTrendlineDataSeries.count() == 0:
                    self.comboBoxTrendlineDataSeries.addItem("No data available for trendlines", userData=None)
                else:
                    # Set range for the auto option
                    self._update_trendline_range()

        except Exception as e:
            logger.error(f"Error populating trendline data series: {e}")

    def _extract_series_from_plot(self):
        """Extract data series directly from plot lines when data frames are empty"""
        try:
            # Check if axes are available
            if not hasattr(self, 'ax') or self.ax is None:
                logger.warning("Axes not available for plot extraction")
                return

            print("Debug: Extracting series from plot lines")

            # Create a temporary data frame from plot lines
            extracted_data = {}

            for i, line in enumerate(self.ax.get_lines()):
                label = line.get_label()
                x_data = line.get_xdata()
                y_data = line.get_ydata()

                # Use label if available, otherwise create a generic name
                if label and not label.startswith('_'):  # Matplotlib uses '_' prefix for auto-generated labels
                    series_name = label
                else:
                    series_name = f"Series {i+1}"

                print(f"Debug: Extracted series '{series_name}' with {len(y_data)} data points")

                # Store the y data (x data is assumed to be consistent)
                extracted_data[series_name] = y_data

                # Add to combo box if suitable
                if len(y_data) > 1:
                    self.comboBoxTrendlineDataSeries.addItem(f"Plot: {series_name}",
                                                           userData={'frame': 'plot', 'series': series_name, 'line_index': i})

            # Store extracted data for later use
            if extracted_data:
                self.extracted_plot_data = extracted_data
                print(f"Debug: Successfully extracted {len(extracted_data)} series from plot")

        except Exception as e:
            logger.error(f"Error extracting series from plot: {e}")

    def _update_trendline_range(self):
        """Update trendline range spinboxes based on selected data series"""
        try:
            current_index = self.comboBoxTrendlineDataSeries.currentIndex()
            if current_index < 0:
                return

            user_data = self.comboBoxTrendlineDataSeries.itemData(current_index)
            if not user_data:
                return

            frame_num = user_data['frame']
            series_name = user_data['series']

            # Handle different data sources
            if frame_num == 'auto' and series_name == 'auto':
                # Get x data from the plot directly
                if hasattr(self, 'ax') and self.ax and self.ax.get_lines():
                    x_data = self.ax.get_lines()[0].get_xdata()
                else:
                    return
            elif frame_num == 'plot':
                # Get data from extracted plot data
                if hasattr(self, 'extracted_plot_data') and series_name in self.extracted_plot_data:
                    data = self.extracted_plot_data[series_name]
                    # Get x data from the corresponding plot line
                    line_index = user_data.get('line_index', 0)
                    if hasattr(self, 'ax') and self.ax and self.ax.get_lines() and line_index < len(self.ax.get_lines()):
                        x_data = self.ax.get_lines()[line_index].get_xdata()
                    else:
                        return
                else:
                    return
            else:
                # Get data from the appropriate frame
                if frame_num == 1 and series_name in self.data_frame_1:
                    data = self.data_frame_1[series_name]
                elif frame_num == 2 and series_name in self.data_frame_2:
                    data = self.data_frame_2[series_name]
                else:
                    return

                # Get x data from the plot (assuming x is consistent across series)
                if hasattr(self, 'ax') and self.ax and self.ax.get_lines():
                    x_data = self.ax.get_lines()[0].get_xdata()
                else:
                    return

            # Validate x_data
            if x_data is not None:
                try:
                    x_data = np.asarray(x_data).flatten()
                    if len(x_data) > 0:
                        x_min, x_max = np.min(x_data), np.max(x_data)

                        # Temporarily disconnect signals to prevent validation loops
                        self._disconnect_range_signals()

                        # Set the values
                        self.trendlineRangeMin.setValue(float(x_min))
                        self.trendlineRangeMax.setValue(float(x_max))

                        # Reconnect signals
                        self._connect_range_signals()

                except Exception as data_error:
                    logger.error(f"Error processing x_data: {data_error}")

        except Exception as e:
            logger.error(f"Error updating trendline range: {e}")
            # Ensure signals are reconnected even if there's an error
            self._connect_range_signals()

    def _validate_trendline_range(self):
        """Validate that min < max for trendline range"""
        try:
            # Temporarily disconnect signals to prevent recursive calls
            self._disconnect_range_signals()

            min_val = self.trendlineRangeMin.value()
            max_val = self.trendlineRangeMax.value()

            # Only auto-correct if the user is editing the min value and it becomes >= max
            sender = self.sender()
            if sender == self.trendlineRangeMin and min_val >= max_val:
                # Set max to min + a reasonable increment
                increment = abs(min_val * 0.1) if min_val != 0 else 1
                self.trendlineRangeMax.setValue(min_val + increment)
            elif sender == self.trendlineRangeMax and max_val <= min_val:
                # Set min to max - a reasonable decrement
                decrement = abs(max_val * 0.1) if max_val != 0 else 1
                self.trendlineRangeMin.setValue(max_val - decrement)

            # Reconnect signals
            self._connect_range_signals()

        except Exception as e:
            logger.error(f"Error validating trendline range: {e}")
            # Reconnect signals even if there's an error
            self._connect_range_signals()

    def _disconnect_range_signals(self):
        """Safely disconnect range validation signals"""
        try:
            # Use blockSignals to prevent signal emission during updates
            if hasattr(self, 'trendlineRangeMin'):
                self.trendlineRangeMin.blockSignals(True)
            if hasattr(self, 'trendlineRangeMax'):
                self.trendlineRangeMax.blockSignals(True)
        except Exception as e:
            logger.debug(f"Error blocking signals: {e}")

    def _connect_range_signals(self):
        """Safely reconnect range validation signals"""
        try:
            # Re-enable signals and connect if needed
            if hasattr(self, 'trendlineRangeMin'):
                self.trendlineRangeMin.blockSignals(False)
                # Ensure connection exists
                try:
                    self.trendlineRangeMin.valueChanged.disconnect(self._validate_trendline_range)
                except:
                    pass  # Wasn't connected
                self.trendlineRangeMin.valueChanged.connect(self._validate_trendline_range)

            if hasattr(self, 'trendlineRangeMax'):
                self.trendlineRangeMax.blockSignals(False)
                # Ensure connection exists
                try:
                    self.trendlineRangeMax.valueChanged.disconnect(self._validate_trendline_range)
                except:
                    pass  # Wasn't connected
                self.trendlineRangeMax.valueChanged.connect(self._validate_trendline_range)
        except Exception as e:
            logger.debug(f"Error connecting signals: {e}")

    def get_trendline_data_selection(self):
        """Get the currently selected data series and range for trendline analysis"""
        try:
            current_index = self.comboBoxTrendlineDataSeries.currentIndex()
            if current_index < 0:
                return None, None, None, None

            user_data = self.comboBoxTrendlineDataSeries.itemData(current_index)
            if not user_data:
                return None, None, None, None

            frame_num = user_data['frame']
            series_name = user_data['series']

            # Handle special cases - return None values to use original behavior or plot extraction
            if frame_num == 'auto' and series_name == 'auto':
                try:
                    range_min = self.trendlineRangeMin.value()
                    range_max = self.trendlineRangeMax.value()
                except (AttributeError, RuntimeError):
                    range_min = None
                    range_max = None
                return None, None, range_min, range_max
            elif frame_num == 'plot':
                # Return plot frame info for extracted data
                try:
                    range_min = self.trendlineRangeMin.value()
                    range_max = self.trendlineRangeMax.value()
                except (AttributeError, RuntimeError):
                    range_min = None
                    range_max = None
                return frame_num, series_name, range_min, range_max

            # Get range values, with fallback to reasonable defaults
            try:
                range_min = self.trendlineRangeMin.value()
                range_max = self.trendlineRangeMax.value()
            except (AttributeError, RuntimeError):
                # Fallback if spinboxes aren't available
                range_min = None
                range_max = None

            return frame_num, series_name, range_min, range_max

        except Exception as e:
            logger.error(f"Error getting trendline data selection: {e}")
            return None, None, None, None

    def setup_icons(self):
        """Setup icons for UI elements"""
        try:
            paths = StylesheetManager.get_base_paths()

            # Combo box style
            combo_style = f"""
                QComboBox::down-arrow {{
                    image: url("{paths['down_arrow']}");
                    width: 24px;
                    height: 24px;
                }}
                QComboBox::drop-down {{
                    border: none;
                    background: transparent;
                    width: 20px;
                    margin-right: 8px;
                }}
            """

            combo_boxes = [
                self.comboBoxDataSelection, self.comboBoxDataSelectionForPlotRange,
                self.comboBoxTitleFont, self.comboBoxLabelFont, self.comboBoxLegendFont,
                self.comboBoxTitleFontSize, self.comboBoxLabelFontSize, self.comboBoxLegendFontSize
            ]

            for combo_box in combo_boxes:
                if combo_box:
                    current_style = combo_box.styleSheet()
                    combo_box.setStyleSheet(current_style + combo_style)

            # Checkbox style
            checkbox_style = f"""
                QCheckBox::indicator {{
                    width: 18px;
                    height: 18px;
                }}
                QCheckBox::indicator:checked {{
                    image: url("{paths['checkbox_checked']}");
                }}
                QCheckBox::indicator:unchecked {{
                    image: url("{paths['checkbox_unchecked']}");
                }}
            """

            checkboxes = [self.checkBoxMajorGrid, self.checkBoxMinorGrid]
            for checkbox in checkboxes:
                if checkbox:
                    current_style = checkbox.styleSheet()
                    checkbox.setStyleSheet(current_style + checkbox_style)

        except Exception as e:
            logger.warning(f"Error setting up icons: {e}")

    def _get_active_axis(self):
        """Get the currently active axis"""
        if hasattr(self, 'data_selection_toggle'):
            index = self.data_selection_toggle.get_current_index()
            return self.ax if index == 0 else self.ax_2
        return self.ax

    def _get_active_data_frame(self):
        """Get the currently active data frame"""
        if hasattr(self, 'data_selection_toggle'):
            index = self.data_selection_toggle.get_current_index()
            return self.data_frame_1 if index == 0 else self.data_frame_2
        return self.data_frame_1

    def _get_original_series_name(self, legend_name):
        """Map legend name back to original series name"""
        try:
            active_df = self._get_active_data_frame()
            for series in active_df.keys():
                if self.settings_manager.settings.get(f'legend_name_{series}', series) == legend_name:
                    return series
        except Exception as e:
            logger.warning(f"Error getting original series name: {e}")
        return None

    def _handle_data_frame_change(self):
        """Handle changes in data frame selection"""
        try:
            self.comboBoxDataSelection.clear()
            current_data_frame = self._get_active_data_frame()

            if current_data_frame:
                for series in current_data_frame.keys():
                    legend_name = self.settings_manager.settings.get(f'legend_name_{series}', series)
                    self.comboBoxDataSelection.addItem(legend_name)

                # Update UI for first series
                first_series = list(current_data_frame.keys())[0]
                series_color = self.settings_manager.settings.get(f'data_series_color_{first_series}', '#008000')
                legend_name = self.settings_manager.settings.get(f'legend_name_{first_series}', first_series)
                self.lnEdtDataSelectionColorHex.setText(series_color)
                self.btnDataSelectionColorPallet.setStyleSheet(f"background-color: {series_color};")
                self.lnEdtLegendName.setText(legend_name)

        except Exception as e:
            logger.error(f"Error handling data frame change: {e}")

    def _dynamic_color_change(self, color):
        """Update color button when hex code is manually changed"""
        try:
            if color and color.startswith('#') and len(color) == 7:
                self.btnTitleFontColorPallet.setStyleSheet(f"background-color: {color};")
                self.settings_manager.settings['title_color'] = color
                self._apply_current_settings()
        except Exception as e:
            logger.warning(f"Error in dynamic color change: {e}")

    def _update_data_series_ui(self):
        """Update UI when different data series is selected"""
        if not self.data_frame_1:
            return

        try:
            selected_series = self._get_original_series_name(self.comboBoxDataSelection.currentText())
            if selected_series:
                series_color = self.settings_manager.settings.get(f'data_series_color_{selected_series}', '#008000')
                legend_name = self.settings_manager.settings.get(f'legend_name_{selected_series}', selected_series)
                self.lnEdtDataSelectionColorHex.setText(series_color)
                self.btnDataSelectionColorPallet.setStyleSheet(f"background-color: {series_color};")
                self.lnEdtLegendName.setText(legend_name)
        except Exception as e:
            logger.error(f"Error updating data series UI: {e}")

    def _update_legend_name(self):
        """Update legend name for current series"""
        try:
            current_legend_name = self.comboBoxDataSelection.currentText()
            series = self._get_original_series_name(current_legend_name)
            if not series:
                return

            new_name = self.lnEdtLegendName.text()
            color = self.lnEdtDataSelectionColorHex.text()

            # Update settings
            self.settings_manager.settings[f'legend_name_{series}'] = new_name
            self.settings_manager.settings[f'data_series_color_{series}'] = color

            # Update combo box
            current_index = self.comboBoxDataSelection.currentIndex()
            if current_index >= 0:
                self.comboBoxDataSelection.setItemText(current_index, new_name)

            self._apply_current_settings()
        except Exception as e:
            logger.error(f"Error updating legend name: {e}")

    def _pick_color(self, button, setting_key, color_line_edit):
        """Handle color picker dialog"""
        try:
            color = QColorDialog.getColor()
            if color.isValid():
                color_name = color.name()
                button.setStyleSheet(f"background-color: {color_name};")
                color_line_edit.setText(color_name)

                # Update settings
                if setting_key == 'data_series_color':
                    series = self._get_original_series_name(self.comboBoxDataSelection.currentText())
                    if series:
                        self.settings_manager.settings[f'data_series_color_{series}'] = color_name
                else:
                    self.settings_manager.settings[setting_key] = color_name

                self._apply_current_settings()
        except Exception as e:
            logger.error(f"Error picking color: {e}")

    def _update_range_spinboxes(self, axis_name):
        """Update range spinboxes when axis selection changes"""
        if not self.ax:
            return

        try:
            # Get current axis limits
            x_min, x_max = self.ax.get_xlim()
            y_min, y_max = self.ax.get_ylim()

            self.settings_manager.settings.update({
                'x_axis_min': x_min, 'x_axis_max': x_max,
                'y_axis_min': y_min, 'y_axis_max': y_max
            })

            if self.ax_2:
                y2_min, y2_max = self.ax_2.get_ylim()
                self.settings_manager.settings.update({
                    'y2_axis_min': y2_min, 'y2_axis_max': y2_max
                })

            # Update spinboxes based on selected axis
            if axis_name == "X-Axis":
                self.customDoubleSpinBoxRangeMin.setValue(x_min)
                self.customDoubleSpinBoxRangeMax.setValue(x_max)
            elif axis_name == "Y-Axis":
                self.customDoubleSpinBoxRangeMin.setValue(y_min)
                self.customDoubleSpinBoxRangeMax.setValue(y_max)
            elif axis_name == "Y-Axis 2" and self.ax_2:
                y2_min, y2_max = self.ax_2.get_ylim()
                self.customDoubleSpinBoxRangeMin.setValue(y2_min)
                self.customDoubleSpinBoxRangeMax.setValue(y2_max)

        except Exception as e:
            logger.error(f"Error updating range spinboxes: {e}")

    def _update_plot_range(self):
        """Update plot range based on spinbox values"""
        if not self.ax:
            return

        try:
            min_val = self.customDoubleSpinBoxRangeMin.value()
            max_val = self.customDoubleSpinBoxRangeMax.value()

            if min_val >= max_val:
                return

            selected_axis = self.comboBoxDataSelectionForPlotRange.currentText()

            if selected_axis == "X-Axis":
                self.settings_manager.settings.update({'x_axis_min': min_val, 'x_axis_max': max_val})
            elif selected_axis == "Y-Axis":
                self.settings_manager.settings.update({'y_axis_min': min_val, 'y_axis_max': max_val})
            elif selected_axis == "Y-Axis 2" and self.ax_2:
                self.settings_manager.settings.update({'y2_axis_min': min_val, 'y2_axis_max': max_val})

            # Apply only axis limits change
            self.renderer._apply_axis_limits(self.settings_manager.settings)
            self.figure.canvas.draw_idle()

        except Exception as e:
            logger.error(f"Error updating plot range: {e}")

    def _collect_current_ui_settings(self):
        """Collect all current UI settings"""
        try:
            # Font settings
            self.settings_manager.settings.update({
                'title_font': self.comboBoxTitleFont.currentFont().family(),
                'title_size': self.settings_manager.safe_convert(self.comboBoxTitleFontSize.currentText(), int, 12),
                'title_color': self.lnEdtTitleFontColorHex.text() or '#000000',

                'label_font': self.comboBoxLabelFont.currentFont().family(),
                'label_size': self.settings_manager.safe_convert(self.comboBoxLabelFontSize.currentText(), int, 10),
                'label_color': self.lnEdtLabelFontColorHex.text() or '#000000',

                'legend_font': self.comboBoxLegendFont.currentFont().family(),
                'legend_size': self.settings_manager.safe_convert(self.comboBoxLegendFontSize.currentText(), int, 10),
                'legend_color': self.lnEdtLegendFontColorHex.text() or '#000000',

                'major_grid_visible': self.checkBoxMajorGrid.isChecked(),
                'minor_grid_visible': self.checkBoxMinorGrid.isChecked(),
                'major_grid_color': self.lnEdtMajorGridColorHex.text() or '#008000',
                'minor_grid_color': self.lnEdtMinorGridColorHex.text() or '#008000'
            })

            # Ensure all data series have settings
            for data_frame in [self.data_frame_1, self.data_frame_2]:
                for series in data_frame.keys():
                    if f'legend_name_{series}' not in self.settings_manager.settings:
                        self.settings_manager.settings[f'legend_name_{series}'] = series
                    if f'data_series_color_{series}' not in self.settings_manager.settings:
                        self.settings_manager.settings[f'data_series_color_{series}'] = '#008000'

        except Exception as e:
            logger.error(f"Error collecting UI settings: {e}")

    def _apply_current_settings(self):
        """Apply current settings to the plot"""
        try:
            self._collect_current_ui_settings()
            self.renderer.apply_settings(
                self.settings_manager.settings,
                self.data_frame_1,
                self.data_frame_2
            )
        except Exception as e:
            logger.error(f"Error applying current settings: {e}")

    def _restore_original_plot(self):
        """Restore original plot state"""
        try:
            for line in self.ax.get_lines():
                if hasattr(line, 'set_data_orig'):
                    line.set_data(*line.set_data_orig)
            self.figure.tight_layout()
            self.figure.canvas.draw()
        except Exception as e:
            logger.error(f"Error restoring original plot: {e}")

    def reject(self):
        """Handle dialog rejection"""
        self._restore_original_plot()
        super().reject()

    def accept_changes(self):
        """Handle dialog acceptance with final settings application"""
        try:
            self._collect_current_ui_settings()
            self.renderer.apply_settings(
                self.settings_manager.settings,
                self.data_frame_1,
                self.data_frame_2
            )
            self.accept()
        except Exception as e:
            logger.error(f"Error accepting changes: {e}")
            self.accept()  # Still accept dialog even if error occurs

    def add_trendline(self, trend_type=None):
        # Get trendline data selection
        frame_num, series_name, range_min, range_max = self.get_trendline_data_selection()

        # Create trendline with data selection parameters
        extracted_plot_data = getattr(self, 'extracted_plot_data', {})
        trendline = PlotTrendline(self.ax, self.ax_2,
                                  data_frame_1=self.data_frame_1,
                                  data_frame_2=self.data_frame_2,
                                  extracted_plot_data=extracted_plot_data)

        if trend_type == 'linear':
            # Get current extrapolation values when the trendline is actually added
            extrapolation_forward, extrapolation_backward = self.get_extrapolation_limits()
            trendline.linear(extrapolation_forward, extrapolation_backward,
                           frame_num=frame_num, series_name=series_name,
                           range_min=range_min, range_max=range_max)
        elif trend_type == 'polynomial':
            trendline.polynomial(degree=int(self.lnEdtPolyDegree.text()),
                               frame_num=frame_num, series_name=series_name,
                               range_min=range_min, range_max=range_max)
        elif trend_type == 'exponential':
            trendline.exponential(frame_num=frame_num, series_name=series_name,
                                range_min=range_min, range_max=range_max)
        elif trend_type == 'logarithmic':
            trendline.logarithmic(frame_num=frame_num, series_name=series_name,
                                range_min=range_min, range_max=range_max)
        elif trend_type == 'power':
            trendline.power(frame_num=frame_num, series_name=series_name,
                          range_min=range_min, range_max=range_max)
        elif trend_type == 'moving_average':
            trendline.moving_average(window_size=int(self.lnEdtMvAvgWindowSize.text()),
                                   frame_num=frame_num, series_name=series_name,
                                   range_min=range_min, range_max=range_max)


# class PlotTrendline:
#     def __init__(self, ax, ax_2=None):
#         self.ax = ax
#         self.ax_2 = ax_2
#
#     def linear(self):
#         """Apply linear trendline to the current plot"""
#         # Implementation for linear trendline
#         x = self.ax.get_lines()[0].get_xdata()
#         y = self.ax.get_lines()[0].get_ydata()
#         slope, intercept, _, _, _ = linregress(x, y)
#         trendline = slope * x + intercept
#         print(f"The equation is: {slope} * x + {intercept}")
#         self.ax.plot(x, trendline, color='red', linestyle='--', label='Linear Trendline')
#
#     def polynomial(self):
#         """Apply polynomial trendline to the current plot"""
#         # Implementation for polynomial trendline
#         x = self.ax.get_lines()[0].get_xdata()
#         y = self.ax.get_lines()[0].get_ydata()
#         degree = 30  # Example degree, can be made dynamic
#         coefficients = np.polyfit(x, y, degree)
#         polynomial = np.poly1d(coefficients)
#         trendline_eqn = polynomial
#         self.draggable = DraggableText(self.ax.text(0.05, 0.95, f"Trendline: {trendline_eqn}", transform=self.ax.transAxes))
#         trendline = polynomial(x)
#         self.ax.plot(x, trendline, color='blue', linestyle='--', label='Polynomial Trendline')


class DraggableText:
    def __init__(self, text_obj):
        self.text = text_obj
        self.press = None
        self.canvas = self.text.figure.canvas
        self.text.set_picker(True)
        self.connect()

    # ---------- connect / disconnect unchanged ----------
    def connect(self):
        self.cid_press = self.canvas.mpl_connect('button_press_event', self.on_press)
        self.cid_release = self.canvas.mpl_connect('button_release_event', self.on_release)
        self.cid_motion = self.canvas.mpl_connect('motion_notify_event', self.on_motion)

    def disconnect(self):
        for cid in (self.cid_press, self.cid_release, self.cid_motion):
            self.canvas.mpl_disconnect(cid)

    # ----------------------------------------------------
    def on_press(self, event):
        if (event.inaxes != self.text.axes or
                event.button != 1):
            return
        contains, _ = self.text.contains(event)
        if contains:
            # store original position in the SAME coordinate system we use
            self.press = (self.text.get_position(), event.x, event.y)

    def on_motion(self, event):
        if self.press is None or event.inaxes != self.text.axes:
            return
        (x0, y0), xpress, ypress = self.press
        dx = event.x - xpress
        dy = event.y - ypress
        # canvas pixels → axes fraction
        trans = self.text.axes.transAxes.inverted()
        dxa, dya = trans.transform((dx, dy)) - trans.transform((0, 0))
        self.text.set_position((x0 + dxa, y0 + dya))
        self.canvas.draw_idle()

    def on_release(self, event):
        self.press = None
        self.canvas.draw_idle()


class PlotTrendline:
    def __init__(self, ax, ax_2=None, data_frame_1=None, data_frame_2=None, extracted_plot_data=None):
        self.ax = ax
        self.ax_2 = ax_2
        self.data_frame_1 = data_frame_1 or {}
        self.data_frame_2 = data_frame_2 or {}
        self.extracted_plot_data = extracted_plot_data or {}

    def _get_data_for_analysis(self, frame_num=None, series_name=None, range_min=None, range_max=None):
        """Get x and y data for trendline analysis based on user selection"""
        try:
            # Default behavior: use first line from plot if no specific selection
            if frame_num is None or series_name is None:
                if not self.ax.get_lines():
                    print("No data lines found in the plot")
                    return None, None
                x = self.ax.get_lines()[0].get_xdata()
                y = self.ax.get_lines()[0].get_ydata()
            else:
                # Get data from specified frame and series
                if frame_num == 1 and series_name in self.data_frame_1:
                    y_data = self.data_frame_1[series_name]
                elif frame_num == 2 and series_name in self.data_frame_2:
                    y_data = self.data_frame_2[series_name]
                elif frame_num == 'plot' and series_name in self.extracted_plot_data:
                    y_data = self.extracted_plot_data[series_name]
                else:
                    print(f"Series '{series_name}' not found in frame {frame_num}")
                    return None, None

                # Get x data from the plot (assuming x is consistent)
                if not self.ax.get_lines():
                    print("No data lines found in the plot for x data")
                    return None, None
                x = self.ax.get_lines()[0].get_xdata()

                # Convert y data to numpy array with better error handling
                try:
                    if isinstance(y_data, np.ndarray):
                        y = y_data
                    elif isinstance(y_data, (list, tuple)):
                        y = np.array(y_data)
                    elif hasattr(y_data, 'values'):  # pandas Series/DataFrame
                        y = np.array(y_data.values)
                    elif hasattr(y_data, '__iter__') and not isinstance(y_data, (str, bytes)):
                        y = np.array(list(y_data))
                    else:
                        # Handle scalar or single value - try to create an array with repeated values
                        if np.isscalar(y_data):
                            # If we have x_data, create y array with same length filled with the scalar value
                            if len(x) > 0:
                                y = np.full(len(x), y_data)
                                print(f"Info: Series '{series_name}' contains scalar data ({y_data}), creating constant array for trendline")
                            else:
                                print(f"Warning: Series '{series_name}' contains scalar data and no x_data available, cannot create trendline")
                                return None, None
                        else:
                            y = np.array([y_data])
                except Exception as conv_error:
                    print(f"Error converting y_data to array: {conv_error}")
                    print(f"y_data type: {type(y_data)}, value: {y_data}")
                    return None, None

            # Convert to numpy arrays and ensure they are 1D
            x = np.asarray(x).flatten()
            y = np.asarray(y).flatten()

            # Check if we have valid data
            if len(x) == 0 or len(y) == 0:
                print("Error: Empty data arrays")
                return None, None

            # Ensure x and y have the same length
            if len(x) != len(y):
                print(f"Warning: x and y data have different lengths ({len(x)} vs {len(y)})")
                min_len = min(len(x), len(y))
                x = x[:min_len]
                y = y[:min_len]

            # Apply range filtering if specified
            if range_min is not None and range_max is not None:
                mask = (x >= range_min) & (x <= range_max)
                x = x[mask]
                y = y[mask]

                if len(x) == 0:
                    print(f"No data points found in range [{range_min}, {range_max}]")
                    return None, None

            return x, y

        except Exception as e:
            print(f"Error getting data for analysis: {e}")
            import traceback
            traceback.print_exc()
            return None, None

    def linear(self, extrapolate_forward=0, extrapolate_backward=0, frame_num=None, series_name=None, range_min=None, range_max=None):
        """Apply linear trendline to the current plot with extrapolation support and data selection"""
        try:
            # Get data based on user selection
            x, y = self._get_data_for_analysis(frame_num, series_name, range_min, range_max)
            if x is None or y is None:
                return

            # Calculate linear regression
            slope, intercept, r_value, p_value, std_err = linregress(x, y)

            # Extrapolation logic - use original data range or selected range
            if range_min is not None and range_max is not None:
                x_min, x_max = range_min, range_max
            else:
                x_min, x_max = np.min(x), np.max(x)

            x_start = x_min - extrapolate_backward if extrapolate_backward else x_min
            x_end = x_max + extrapolate_forward if extrapolate_forward else x_max

            # Create extended x range for trendline (including extrapolation)
            x_trend = np.linspace(x_start, x_end, 200)
            y_trend = slope * x_trend + intercept

            # Create equation text with range info
            range_info = ""
            if range_min is not None and range_max is not None:
                range_info = f"\nRange: [{range_min:.2f}, {range_max:.2f}]"
            if series_name:
                range_info += f"\nSeries: {series_name}"

            equation_text = f"y = {slope:.3f}x + {intercept:.3f}\nR² = {r_value ** 2:.3f}{range_info}"

            # Plot the trendline using the correct x and y arrays
            label = f'Linear Trendline ({series_name})' if series_name else 'Linear Trendline'
            self.ax.plot(x_trend, y_trend, color='red', linestyle='--', label=label)

            # Add draggable equation text
            text_obj = self.ax.text(0.05, 0.95, equation_text,
                                    transform=self.ax.transAxes,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                    fontsize=10)

            # Make the text draggable
            # keep a reference so the object is not garbage-collected
            self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

            # Refresh the plot
            self.ax.figure.canvas.draw()

            print(f"Linear trendline added - Series: {series_name}, Range: [{range_min}, {range_max}], "
                  f"Extrapolation: forward={extrapolate_forward}, backward={extrapolate_backward}")

        except Exception as e:
            print(f"Error creating linear trendline: {e}")
            import traceback
            traceback.print_exc()

    def _format_polynomial_equation(self, coefficients):
        """Format polynomial coefficients into a readable equation string"""
        degree = len(coefficients) - 1
        terms = []

        for i, coeff in enumerate(coefficients):
            power = degree - i

            if abs(coeff) < 1e-10:  # Skip very small coefficients
                continue

            # Format coefficient
            if power == 0:
                terms.append(f"{coeff:.4f}")
            elif power == 1:
                if coeff == 1:
                    terms.append("x")
                elif coeff == -1:
                    terms.append("-x")
                else:
                    terms.append(f"{coeff:.4f}x")
            else:
                if coeff == 1:
                    terms.append(f"x^{power}")
                elif coeff == -1:
                    terms.append(f"-x^{power}")
                else:
                    terms.append(f"{coeff:.4f}x^{power}")

        if not terms:
            return "y = 0"

        equation = "y = " + terms[0]
        for term in terms[1:]:
            if term.startswith('-'):
                equation += f" - {term[1:]}"
            else:
                equation += f" + {term}"

        return equation

    def exponential(self, frame_num=None, series_name=None, range_min=None, range_max=None):
        """Apply exponential trendline to the current plot with data selection"""
        try:
            # Get data based on user selection
            x, y = self._get_data_for_analysis(frame_num, series_name, range_min, range_max)
            if x is None or y is None:
                return

            # Filter out non-positive y values for logarithmic transformation
            mask = y > 0
            if not np.any(mask):
                print("No positive y values found for exponential fit")
                return

            x_filtered = x[mask]
            y_filtered = y[mask]

            # Fit exponential: y = a * exp(b * x)
            # Take log: ln(y) = ln(a) + b * x
            log_y = np.log(y_filtered)
            b, ln_a = np.polyfit(x_filtered, log_y, 1)
            a = np.exp(ln_a)

            # Generate trendline
            x_trend = np.linspace(x.min(), x.max(), 100)
            y_trend = a * np.exp(b * x_trend)

            # Calculate R² for the exponential fit
            y_pred = a * np.exp(b * x_filtered)
            ss_res = np.sum((y_filtered - y_pred) ** 2)
            ss_tot = np.sum((y_filtered - np.mean(y_filtered)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

            # Create equation text with range info
            range_info = ""
            if range_min is not None and range_max is not None:
                range_info = f"\nRange: [{range_min:.2f}, {range_max:.2f}]"
            if series_name:
                range_info += f"\nSeries: {series_name}"

            equation_text = f"y = {a:.3f}e^({b:.3f}x)\nR² = {r_squared:.3f}{range_info}"

            # Plot the trendline
            label = f'Exponential Trendline ({series_name})' if series_name else 'Exponential Trendline'
            self.ax.plot(x_trend, y_trend, color='green', linestyle='--', label=label)

            # Add draggable equation text
            text_obj = self.ax.text(0.05, 0.95, equation_text,
                                    transform=self.ax.transAxes,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                    fontsize=10)

            # Make the text draggable
            # keep a reference so the object is not garbage-collected
            self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

            # Refresh the plot
            self.ax.figure.canvas.draw()

            print(f"Exponential trendline added - Series: {series_name}, Range: [{range_min}, {range_max}]")

        except Exception as e:
            print(f"Error creating exponential trendline: {e}")

    def logarithmic(self, frame_num=None, series_name=None, range_min=None, range_max=None):
            """Apply logarithmic trendline to the current plot with data selection"""
            try:
                # Get data based on user selection
                x, y = self._get_data_for_analysis(frame_num, series_name, range_min, range_max)
                if x is None or y is None:
                    return

                # Filter out non-positive x values for logarithmic transformation
                mask = x > 0
                if not np.any(mask):
                    print("No positive x values found for logarithmic fit")
                    return

                x_filtered = x[mask]
                y_filtered = y[mask]

                # Fit logarithmic: y = a * ln(x) + b
                log_x = np.log(x_filtered)
                a, b = np.polyfit(log_x, y_filtered, 1)

                # Generate trendline
                x_trend = np.linspace(x_filtered.min(), x_filtered.max(), 100)
                y_trend = a * np.log(x_trend) + b

                # Calculate R²
                y_pred = a * np.log(x_filtered) + b
                ss_res = np.sum((y_filtered - y_pred) ** 2)
                ss_tot = np.sum((y_filtered - np.mean(y_filtered)) ** 2)
                r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

                # Create equation text with range info
                range_info = ""
                if range_min is not None and range_max is not None:
                    range_info = f"\nRange: [{range_min:.2f}, {range_max:.2f}]"
                if series_name:
                    range_info += f"\nSeries: {series_name}"

                equation_text = f"y = {a:.3f}ln(x) + {b:.3f}\nR² = {r_squared:.3f}{range_info}"

                # Plot the trendline
                label = f'Logarithmic Trendline ({series_name})' if series_name else 'Logarithmic Trendline'
                self.ax.plot(x_trend, y_trend, color='purple', linestyle='--', label=label)

                # Add draggable equation text
                text_obj = self.ax.text(0.05, 0.95, equation_text,
                                        transform=self.ax.transAxes,
                                        bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                        fontsize=10)

                # Make the text draggable
                # keep a reference so the object is not garbage-collected
                self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

                # Refresh the plot
                self.ax.figure.canvas.draw()

                print(f"Logarithmic trendline added - Series: {series_name}, Range: [{range_min}, {range_max}]")

            except Exception as e:
                print(f"Error creating logarithmic trendline: {e}")

    def power(self, frame_num=None, series_name=None, range_min=None, range_max=None):
            """Apply power trendline to the current plot with data selection"""
            try:
                # Get data based on user selection
                x, y = self._get_data_for_analysis(frame_num, series_name, range_min, range_max)
                if x is None or y is None:
                    return

                # Filter out non-positive values for logarithmic transformation
                mask = (x > 0) & (y > 0)
                if not np.any(mask):
                    print("No positive x and y values found for power fit")
                    return

                x_filtered = x[mask]
                y_filtered = y[mask]

                # Fit power: y = a * x^b
                log_x = np.log(x_filtered)
                log_y = np.log(y_filtered)

                b, log_a = np.polyfit(log_x, log_y, 1)
                a = np.exp(log_a)

                # Generate trendline
                x_trend = np.linspace(x_filtered.min(), x_filtered.max(), 100)
                y_trend = a * (x_trend ** b)

                # Calculate R²
                y_pred = a * (x_filtered ** b)
                ss_res = np.sum((y_filtered - y_pred) ** 2)
                ss_tot = np.sum((y_filtered - np.mean(y_filtered)) ** 2)
                r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

                # Create equation text with range info
                range_info = ""
                if range_min is not None and range_max is not None:
                    range_info = f"\nRange: [{range_min:.2f}, {range_max:.2f}]"
                if series_name:
                    range_info += f"\nSeries: {series_name}"

                equation_text = f"y = {a:.3f}x^{b:.3f}\nR² = {r_squared:.3f}{range_info}"

                # Plot the trendline
                label = f'Power Trendline ({series_name})' if series_name else 'Power Trendline'
                self.ax.plot(x_trend, y_trend, color='orange', linestyle='--', label=label)

                # Add draggable equation text
                text_obj = self.ax.text(0.05, 0.95, equation_text,
                                        transform=self.ax.transAxes,
                                        bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                        fontsize=10)

                # Make the text draggable
                # keep a reference so the object is not garbage-collected
                self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

                # Refresh the plot
                self.ax.figure.canvas.draw()

                print(f"Power trendline added - Series: {series_name}, Range: [{range_min}, {range_max}]")

            except Exception as e:
                print(f"Error creating power trendline: {e}")

    def moving_average(self, window_size=5, frame_num=None, series_name=None, range_min=None, range_max=None):
            """Apply moving average trendline to the current plot with data selection"""
            try:
                # Get data based on user selection
                x, y = self._get_data_for_analysis(frame_num, series_name, range_min, range_max)
                if x is None or y is None:
                    return

                # Calculate moving average
                moving_avg = np.convolve(y, np.ones(window_size) / window_size, mode='valid')

                # Generate x values for the moving average
                x_trend = x[window_size - 1:]

                # Create equation text with range info
                range_info = ""
                if range_min is not None and range_max is not None:
                    range_info = f"\nRange: [{range_min:.2f}, {range_max:.2f}]"
                if series_name:
                    range_info += f"\nSeries: {series_name}"

                equation_text = f"Moving Average (Window: {window_size}){range_info}"

                # Plot the trendline
                label = f'Moving Average ({series_name})' if series_name else 'Moving Average Trendline'
                self.ax.plot(x_trend, moving_avg, color='cyan', linestyle='--', label=label)

                # Add draggable equation text
                text_obj = self.ax.text(0.05, 0.95, equation_text,
                                        transform=self.ax.transAxes,
                                        bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                        fontsize=10)

                # Make the text draggable
                # keep a reference so the object is not garbage-collected
                self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

                # Refresh the plot
                self.ax.figure.canvas.draw()

                print(
                    f"Moving average trendline added - Series: {series_name}, Range: [{range_min}, {range_max}], Window: {window_size}")

            except Exception as e:
                print(f"Error creating moving average trendline: {e}")

    def polynomial(self, degree=2, frame_num=None, series_name=None, range_min=None, range_max=None):
        """Apply polynomial trendline to the current plot with data selection"""
        try:
            # Get data based on user selection
            x, y = self._get_data_for_analysis(frame_num, series_name, range_min, range_max)
            if x is None or y is None:
                return

            # Fit polynomial (reduced degree for better readability)
            coefficients = np.polyfit(x, y, degree)
            polynomial = np.poly1d(coefficients)

            # Create equation string
            equation_str = self._format_polynomial_equation(coefficients)

            # Calculate R²
            y_pred = polynomial(x)
            ss_res = np.sum((y - y_pred) ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

            # Create equation text with range info
            range_info = ""
            if range_min is not None and range_max is not None:
                range_info = f"\nRange: [{range_min:.2f}, {range_max:.2f}]"
            if series_name:
                range_info += f"\nSeries: {series_name}"

            equation_text = f"{equation_str}\nR² = {r_squared:.3f}{range_info}"

            # Plot the trendline - extend slightly beyond data range for better visualization
            x_plot = np.linspace(np.min(x), np.max(x), 200)
            trendline = polynomial(x_plot)
            label = f'Polynomial Trendline ({series_name})' if series_name else 'Polynomial Trendline'
            self.ax.plot(x_plot, trendline, color='blue', linestyle='--', label=label)

            # Add draggable equation text
            text_obj = self.ax.text(0.05, 0.95, equation_text,
                                    transform=self.ax.transAxes,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                    fontsize=10)

            # Make the text draggable
            # keep a reference so the object is not garbage-collected
            self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

            # Refresh the plot
            self.ax.figure.canvas.draw()

            print(f"Polynomial trendline added - Series: {series_name}, Range: [{range_min}, {range_max}], Degree: {degree}")

        except Exception as e:
            print(f"Error creating polynomial trendline: {e}")


# ---------- Interactive Guideline Manager ----------
class LineManager:
    """
    Enhanced interactive horizontal/vertical lines with:
    - Double-click + drag creation (asks for orientation)
    - Click to select and drag existing lines (axis-constrained)
    - Persistent style memory for next lines
    - Right-click context menu: color, width, linestyle, lock/unlock, delete
    - Snap-to-data-points toggle
    - Export/Import/Clear
    - Arrow head support with customizable styles
    - Enhanced line style options
    - Improved user interaction
    """

    def __init__(self, canvas, parent=None):
        self.canvas = canvas
        self.figure = canvas.figure
        self.parent = parent
        self.ax = self.figure.axes[0] if self.figure.axes else self.figure.gca()

        # State
        self.enabled = False
        self.creating = False
        self.dragging = False
        self.selected = None  # (line, orientation)
        self.current_orientation = None  # 'h' or 'v'
        self.snap = False
        self.locked = set()
        self.lines = []  # list of line artists
        self.arrows = []  # list of arrow annotations

        # Enhanced style settings with persistence
        self._last_style = {
            "color": "#000000",
            "linestyle": "-",
            "linewidth": 1.5,
            "arrow_style": "none",  # none, start, end, both
            "arrow_size": 10,
            "alpha": 0.95
        }

        # Load saved settings
        self._load_settings()

        # IDs
        self._cid_press = None
        self._cid_release = None
        self._cid_motion = None

    # ---------- Settings Persistence ----------
    def _get_settings_path(self):
        """Get path for storing line manager settings"""
        base = Path(os.getenv("APPDATA") or (Path.home() / ".local" / "share"))
        return base / "plottool" / "line_settings.json"

    def _load_settings(self):
        """Load saved line style settings"""
        try:
            settings_path = self._get_settings_path()
            if settings_path.exists():
                with open(settings_path, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self._last_style.update(saved_settings)
        except Exception as e:
            logger.warning(f"Could not load line settings: {e}")

    def _save_settings(self):
        """Save current line style settings"""
        try:
            settings_path = self._get_settings_path()
            _ensure_parent(settings_path)
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(self._last_style, f, indent=2)
        except Exception as e:
            logger.warning(f"Could not save line settings: {e}")

    # ---------- Public API ----------
    def set_drawing_mode(self, on: bool):
        self.enabled = bool(on)
        if on and self._cid_press is None:
            self._cid_press = self.canvas.mpl_connect('button_press_event', self._on_press)
            self._cid_release = self.canvas.mpl_connect('button_release_event', self._on_release)
            self._cid_motion = self.canvas.mpl_connect('motion_notify_event', self._on_motion)
        elif not on and self._cid_press is not None:
            self.canvas.mpl_disconnect(self._cid_press)
            self.canvas.mpl_disconnect(self._cid_release)
            self.canvas.mpl_disconnect(self._cid_motion)
            self._cid_press = self._cid_release = self._cid_motion = None
        # clear transient states
        self.creating = False
        self.dragging = False
        self.selected = None
        self.current_orientation = None

    def set_snap(self, on: bool):
        self.snap = bool(on)

    def export_lines(self, path: str):
        """Export lines with enhanced styling information"""
        data = []
        for ln in self.lines:
            orient = 'h' if self._is_horizontal(ln) else 'v'
            coord = ln.get_ydata()[0] if orient == 'h' else ln.get_xdata()[0]
            data.append({
                "orientation": orient,
                "coord": float(coord),
                "color": ln.get_color(),
                "linestyle": ln.get_linestyle(),
                "linewidth": float(ln.get_linewidth()),
                "arrow_style": getattr(ln, '_arrow_style', 'none'),
                "arrow_size": getattr(ln, '_arrow_size', 10),
                "alpha": ln.get_alpha() or 1.0,
                "locked": bool(ln in self.locked)
            })

        # Also save current style settings
        export_data = {
            "lines": data,
            "default_style": self._last_style.copy()
        }

        with open(path, "w", encoding="utf-8") as f:
            json.dump(export_data, f, indent=2)

    def import_lines(self, path: str):
        """Import lines with enhanced styling information"""
        with open(path, "r", encoding="utf-8") as f:
            data = json.load(f)

        # Handle both old and new format
        if isinstance(data, list):
            # Old format - just lines
            items = data
        else:
            # New format - lines and settings
            items = data.get("lines", [])
            if "default_style" in data:
                self._last_style.update(data["default_style"])
                self._save_settings()

        for it in items:
            orient = it.get("orientation", "h")
            coord = float(it.get("coord", 0))
            ln = self._make_line(
                orient, coord,
                color=it.get("color", "#000000"),
                linestyle=it.get("linestyle", "-"),
                linewidth=float(it.get("linewidth", 1.5)),
                arrow_style=it.get("arrow_style", "none"),
                arrow_size=float(it.get("arrow_size", 10)),
                alpha=float(it.get("alpha", 1.0))
            )
            if it.get("locked"):
                self.locked.add(ln)
        self.canvas.draw_idle()

    def clear(self):
        for ln in list(self.lines):
            try:
                ln.remove()
            except Exception:
                pass
        self.lines.clear()
        self.locked.clear()
        self.selected = None
        self.canvas.draw_idle()

    # ---------- Internal helpers ----------
    def _orientation_menu(self, event):
        # Build menu at mouse global pos
        # Use a proper Qt widget as parent - try parent first, then canvas parent
        parent_widget = None
        if hasattr(self.parent, 'parent') and self.parent.parent:
            parent_widget = self.parent.parent
        elif hasattr(self.canvas, 'parent') and self.canvas.parent():
            parent_widget = self.canvas.parent()

        menu = QMenu(parent_widget)
        act_h = menu.addAction("Horizontal Line")
        act_v = menu.addAction("Vertical Line")
        global_pt = self.canvas.mapToGlobal(QPoint(int(event.x), int(event.y)))
        action = menu.exec(global_pt)
        if action == act_h:
            return 'h'
        elif action == act_v:
            return 'v'
        return None

    def _make_line(self, orient: str, coord: float, color=None, linestyle=None, linewidth=None,
                   arrow_style=None, arrow_size=None, alpha=None):
        """Create a line with optional arrow styling"""
        color = color or self._last_style["color"]
        linestyle = linestyle or self._last_style["linestyle"]
        linewidth = linewidth or self._last_style["linewidth"]
        arrow_style = arrow_style or self._last_style["arrow_style"]
        arrow_size = arrow_size or self._last_style["arrow_size"]
        alpha = alpha or self._last_style["alpha"]

        if orient == 'h':
            ln = self.ax.axhline(coord, color=color, linestyle=linestyle, linewidth=linewidth,
                                alpha=alpha, zorder=9999, picker=5)
            # full x-span
            xmin, xmax = self.ax.get_xlim()
            ln.set_xdata([xmin, xmax])
        else:
            ln = self.ax.axvline(coord, color=color, linestyle=linestyle, linewidth=linewidth,
                                alpha=alpha, zorder=9999, picker=5)
            ymin, ymax = self.ax.get_ylim()
            ln.set_ydata([ymin, ymax])

        ln._is_guideline = True  # mark
        ln._arrow_style = arrow_style
        ln._arrow_size = arrow_size
        self.lines.append(ln)

        # Add arrows if requested
        if arrow_style != "none":
            self._add_arrows_to_line(ln, orient, arrow_style, arrow_size, color)

        return ln

    def _add_arrows_to_line(self, line, orient, arrow_style, arrow_size, color):
        """Add arrow heads to a line"""
        try:
            if orient == 'h':
                y_coord = line.get_ydata()[0]
                xmin, xmax = self.ax.get_xlim()

                if arrow_style in ["start", "both"]:
                    arrow = self.ax.annotate('', xy=(xmin, y_coord), xytext=(xmin + (xmax-xmin)*0.05, y_coord),
                                           arrowprops=dict(arrowstyle='->', color=color, lw=line.get_linewidth(),
                                                         mutation_scale=arrow_size))
                    arrow._is_guideline_arrow = True
                    arrow._parent_line = line
                    self.arrows.append(arrow)

                if arrow_style in ["end", "both"]:
                    arrow = self.ax.annotate('', xy=(xmax, y_coord), xytext=(xmax - (xmax-xmin)*0.05, y_coord),
                                           arrowprops=dict(arrowstyle='->', color=color, lw=line.get_linewidth(),
                                                         mutation_scale=arrow_size))
                    arrow._is_guideline_arrow = True
                    arrow._parent_line = line
                    self.arrows.append(arrow)
            else:  # vertical
                x_coord = line.get_xdata()[0]
                ymin, ymax = self.ax.get_ylim()

                if arrow_style in ["start", "both"]:
                    arrow = self.ax.annotate('', xy=(x_coord, ymin), xytext=(x_coord, ymin + (ymax-ymin)*0.05),
                                           arrowprops=dict(arrowstyle='->', color=color, lw=line.get_linewidth(),
                                                         mutation_scale=arrow_size))
                    arrow._is_guideline_arrow = True
                    arrow._parent_line = line
                    self.arrows.append(arrow)

                if arrow_style in ["end", "both"]:
                    arrow = self.ax.annotate('', xy=(x_coord, ymax), xytext=(x_coord, ymax - (ymax-ymin)*0.05),
                                           arrowprops=dict(arrowstyle='->', color=color, lw=line.get_linewidth(),
                                                         mutation_scale=arrow_size))
                    arrow._is_guideline_arrow = True
                    arrow._parent_line = line
                    self.arrows.append(arrow)
        except Exception as e:
            logger.warning(f"Error adding arrows to line: {e}")

    def _pick_line(self, event):
        for ln in self.lines:
            if ln in self.locked:
                continue
            try:
                contains, _ = ln.contains(event)
            except Exception:
                contains = False
            if contains:
                return ln
        return None

    def _is_horizontal(self, ln):
        # axhline has constant y
        y = ln.get_ydata()
        return len(y) >= 2 and np.allclose(y[0], y[-1], equal_nan=False)

    def _nearest_coord(self, orient: str, event):
        if not self.snap:
            return event.ydata if orient == 'h' else event.xdata
        # snap to nearest data point among non-guideline lines
        best = None
        best_dist = float("inf")
        for ln in self.ax.get_lines():
            if getattr(ln, "_is_guideline", False):
                continue
            x = np.asarray(ln.get_xdata(), dtype=float)
            y = np.asarray(ln.get_ydata(), dtype=float)
            if orient == 'h' and np.isfinite(event.ydata):
                if y.size == 0:
                    continue
                idx = int(np.nanargmin(np.abs(y - event.ydata)))
                cand = float(y[idx])
                dist = abs(cand - event.ydata)
                if dist < best_dist:
                    best_dist = dist;
                    best = cand
            elif orient == 'v' and np.isfinite(event.xdata):
                if x.size == 0:
                    continue
                idx = int(np.nanargmin(np.abs(x - event.xdata)))
                cand = float(x[idx])
                dist = abs(cand - event.xdata)
                if dist < best_dist:
                    best_dist = dist;
                    best = cand
        return best if best is not None else (event.ydata if orient == 'h' else event.xdata)

    # ---------- Events ----------
    def _on_press(self, event):
        if event.inaxes != self.ax:
            return
        if event.button == 3:
            ln = self._pick_line(event)
            if ln is not None:
                self._line_context_menu(event, ln)
            return
        if event.button != 1:
            return

        # Double-click to start creation
        if getattr(event, "dblclick", False):
            orient = self._orientation_menu(event)
            if orient is None:
                return
            coord = self._nearest_coord(orient, event)
            ln = self._make_line(orient, coord)
            self.selected = (ln, orient)
            self.creating = True
            self.canvas.draw_idle()
        else:
            # pick existing for drag
            ln = self._pick_line(event)
            if ln is not None:
                orient = 'h' if self._is_horizontal(ln) else 'v'
                self.selected = (ln, orient)
                self.dragging = True

    def _on_motion(self, event):
        if event.inaxes != self.ax:
            return
        if self.creating and self.selected:
            ln, orient = self.selected
            coord = self._nearest_coord(orient, event)
            if orient == 'h':
                ln.set_ydata([coord, coord])
            else:
                ln.set_xdata([coord, coord])
            self._span_line_to_axes(ln)
            self.canvas.draw_idle()
        elif self.dragging and self.selected:
            ln, orient = self.selected
            coord = self._nearest_coord(orient, event)
            if orient == 'h':
                ln.set_ydata([coord, coord])
            else:
                ln.set_xdata([coord, coord])
            self._span_line_to_axes(ln)
            self.canvas.draw_idle()

    def _on_release(self, event):
        self.creating = False
        self.dragging = False
        self.selected = None

    def _span_line_to_axes(self, ln):
        """Update line to span full axes and update associated arrows"""
        if self._is_horizontal(ln):
            xmin, xmax = self.ax.get_xlim()
            ln.set_xdata([xmin, xmax])
            # Update arrow positions
            self._update_horizontal_arrows(ln)
        else:
            ymin, ymax = self.ax.get_ylim()
            ln.set_ydata([ymin, ymax])
            # Update arrow positions
            self._update_vertical_arrows(ln)

    def _update_horizontal_arrows(self, line):
        """Update arrow positions for horizontal lines"""
        y_coord = line.get_ydata()[0]
        xmin, xmax = self.ax.get_xlim()

        for arrow in self.arrows:
            if getattr(arrow, '_parent_line', None) == line:
                # Determine if this is start or end arrow based on position
                current_x = arrow.xy[0]
                if current_x <= (xmin + xmax) / 2:  # Start arrow
                    arrow.xy = (xmin, y_coord)
                    arrow.xytext = (xmin + (xmax-xmin)*0.05, y_coord)
                else:  # End arrow
                    arrow.xy = (xmax, y_coord)
                    arrow.xytext = (xmax - (xmax-xmin)*0.05, y_coord)

    def _update_vertical_arrows(self, line):
        """Update arrow positions for vertical lines"""
        x_coord = line.get_xdata()[0]
        ymin, ymax = self.ax.get_ylim()

        for arrow in self.arrows:
            if getattr(arrow, '_parent_line', None) == line:
                # Determine if this is start or end arrow based on position
                current_y = arrow.xy[1]
                if current_y <= (ymin + ymax) / 2:  # Start arrow
                    arrow.xy = (x_coord, ymin)
                    arrow.xytext = (x_coord, ymin + (ymax-ymin)*0.05)
                else:  # End arrow
                    arrow.xy = (x_coord, ymax)
                    arrow.xytext = (x_coord, ymax - (ymax-ymin)*0.05)

    # ---------- Context menu for a line ----------
    def _line_context_menu(self, event, ln):
        # Use a proper Qt widget as parent - try parent first, then canvas parent
        parent_widget = None
        if hasattr(self.parent, 'parent') and self.parent.parent:
            parent_widget = self.parent.parent
        elif hasattr(self.canvas, 'parent') and self.canvas.parent():
            parent_widget = self.canvas.parent()

        menu = QMenu(parent_widget)

        # Style options
        act_color = menu.addAction("Color…")
        act_width = menu.addAction("Line width…")

        # Line style submenu
        style_menu = menu.addMenu("Line style")
        style_options = [("-", "Solid"), ("--", "Dashed"), ("-.", "Dash-dot"), (":", "Dotted")]
        current_style = ln.get_linestyle()
        style_actions = {}
        for style_code, style_name in style_options:
            action = style_menu.addAction(style_name)
            action.setCheckable(True)
            action.setChecked(current_style == style_code)
            style_actions[action] = style_code

        # Arrow style submenu
        arrow_menu = menu.addMenu("Arrow style")
        arrow_options = [("none", "No arrows"), ("start", "Start arrow"), ("end", "End arrow"), ("both", "Both arrows")]
        current_arrow = getattr(ln, '_arrow_style', 'none')
        arrow_actions = {}
        for arrow_code, arrow_name in arrow_options:
            action = arrow_menu.addAction(arrow_name)
            action.setCheckable(True)
            action.setChecked(current_arrow == arrow_code)
            arrow_actions[action] = arrow_code

        act_arrow_size = menu.addAction("Arrow size…")
        act_alpha = menu.addAction("Transparency…")

        menu.addSeparator()

        # Utility options
        is_locked = ln in self.locked
        act_lock = menu.addAction("Unlock" if is_locked else "Lock")
        act_duplicate = menu.addAction("Duplicate")
        act_delete = menu.addAction("Delete")

        global_pt = self.canvas.mapToGlobal(QPoint(int(event.x), int(event.y)))
        chosen = menu.exec(global_pt)

        if chosen is None:
            return

        # Handle style actions
        if chosen in style_actions:
            new_style = style_actions[chosen]
            ln.set_linestyle(new_style)
            self._last_style["linestyle"] = new_style
            self._save_settings()
        elif chosen in arrow_actions:
            new_arrow_style = arrow_actions[chosen]
            self._update_line_arrows(ln, new_arrow_style)
            self._last_style["arrow_style"] = new_arrow_style
            self._save_settings()
        elif chosen == act_color:
            col = QColorDialog.getColor(QtGui.QColor(ln.get_color()), self.parent or self.canvas)
            if col.isValid():
                ln.set_color(col.name())
                self._update_arrow_colors(ln, col.name())
                self._last_style["color"] = col.name()
                self._save_settings()
        elif chosen == act_width:
            val, ok = QtWidgets.QInputDialog.getDouble(self.parent or self.canvas, "Line width", "Width",
                                                       float(ln.get_linewidth()), 0.2, 20.0, 1)
            if ok:
                ln.set_linewidth(val)
                self._update_arrow_widths(ln, val)
                self._last_style["linewidth"] = float(val)
                self._save_settings()
        elif chosen == act_arrow_size:
            current_size = getattr(ln, '_arrow_size', 10)
            val, ok = QtWidgets.QInputDialog.getDouble(self.parent or self.canvas, "Arrow size", "Size",
                                                       float(current_size), 5.0, 50.0, 1)
            if ok:
                ln._arrow_size = val
                self._update_line_arrows(ln, getattr(ln, '_arrow_style', 'none'))
                self._last_style["arrow_size"] = float(val)
                self._save_settings()
        elif chosen == act_alpha:
            current_alpha = ln.get_alpha() or 1.0
            val, ok = QtWidgets.QInputDialog.getDouble(self.parent or self.canvas, "Transparency",
                                                       "Alpha (0=transparent, 1=opaque)",
                                                       float(current_alpha), 0.1, 1.0, 2)
            if ok:
                ln.set_alpha(val)
                self._update_arrow_alpha(ln, val)
                self._last_style["alpha"] = float(val)
                self._save_settings()
        elif chosen == act_lock:
            if is_locked:
                self.locked.discard(ln)
            else:
                self.locked.add(ln)
        elif chosen == act_duplicate:
            self._duplicate_line(ln)
        elif chosen == act_delete:
            self._delete_line(ln)

        self.canvas.draw_idle()

    # def show_settings_dialog(self):
    #     """Show line settings dialog"""
    #     try:
    #         dialog = LineSettingsDialog(self, self.parent or self.canvas)
    #         if dialog.exec() == QtWidgets.QDialog.DialogCode.Accepted:
    #             # Settings are automatically applied through the dialog
    #             self._save_settings()
    #     except Exception as e:
    #         logger.error(f"Error showing line settings dialog: {e}")

    # ---------- Arrow and Line Management Helpers ----------
    def _update_line_arrows(self, line, arrow_style):
        """Update arrows for a specific line"""
        # Remove existing arrows for this line
        self._remove_arrows_for_line(line)

        # Add new arrows if needed
        if arrow_style != "none":
            orient = 'h' if self._is_horizontal(line) else 'v'
            arrow_size = getattr(line, '_arrow_size', self._last_style["arrow_size"])
            self._add_arrows_to_line(line, orient, arrow_style, arrow_size, line.get_color())

        line._arrow_style = arrow_style

    def _remove_arrows_for_line(self, line):
        """Remove all arrows associated with a specific line"""
        arrows_to_remove = [arrow for arrow in self.arrows if getattr(arrow, '_parent_line', None) == line]
        for arrow in arrows_to_remove:
            try:
                arrow.remove()
            except Exception:
                pass
            if arrow in self.arrows:
                self.arrows.remove(arrow)

    def _update_arrow_colors(self, line, color):
        """Update color of arrows associated with a line"""
        for arrow in self.arrows:
            if getattr(arrow, '_parent_line', None) == line:
                arrow.set_color(color)
                if hasattr(arrow, 'arrow_patch'):
                    arrow.arrow_patch.set_color(color)

    def _update_arrow_widths(self, line, width):
        """Update width of arrows associated with a line"""
        for arrow in self.arrows:
            if getattr(arrow, '_parent_line', None) == line:
                if hasattr(arrow, 'arrowprops'):
                    arrow.arrowprops['lw'] = width

    def _update_arrow_alpha(self, line, alpha):
        """Update alpha of arrows associated with a line"""
        for arrow in self.arrows:
            if getattr(arrow, '_parent_line', None) == line:
                arrow.set_alpha(alpha)

    def _duplicate_line(self, original_line):
        """Create a duplicate of an existing line"""
        try:
            orient = 'h' if self._is_horizontal(original_line) else 'v'
            coord = original_line.get_ydata()[0] if orient == 'h' else original_line.get_xdata()[0]

            # Offset the duplicate slightly
            if orient == 'h':
                y_range = self.ax.get_ylim()
                offset = (y_range[1] - y_range[0]) * 0.02
                coord += offset
            else:
                x_range = self.ax.get_xlim()
                offset = (x_range[1] - x_range[0]) * 0.02
                coord += offset

            # Create duplicate with same style
            new_line = self._make_line(
                orient, coord,
                color=original_line.get_color(),
                linestyle=original_line.get_linestyle(),
                linewidth=original_line.get_linewidth(),
                arrow_style=getattr(original_line, '_arrow_style', 'none'),
                arrow_size=getattr(original_line, '_arrow_size', 10),
                alpha=original_line.get_alpha() or 1.0
            )

            self.canvas.draw_idle()
        except Exception as e:
            logger.warning(f"Error duplicating line: {e}")

    def _delete_line(self, line):
        """Delete a line and its associated arrows"""
        try:
            # Remove associated arrows
            self._remove_arrows_for_line(line)

            # Remove the line
            line.remove()
            if line in self.lines:
                self.lines.remove(line)
            self.locked.discard(line)
        except Exception as e:
            logger.warning(f"Error deleting line: {e}")

    def clear(self):
        """Clear all lines and arrows"""
        # Clear arrows
        for arrow in list(self.arrows):
            try:
                arrow.remove()
            except Exception:
                pass
        self.arrows.clear()

        # Clear lines
        for ln in list(self.lines):
            try:
                ln.remove()
            except Exception:
                pass
        self.lines.clear()
        self.locked.clear()
        self.selected = None
        self.canvas.draw_idle()




