#!/usr/bin/env python3
"""
Simple test to verify context menu fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_context_menu_fixes():
    """Test that context menu methods are properly implemented"""
    print("Testing Context Menu Fixes...")
    print("=" * 30)
    
    try:
        from gui.dialog.custom_plot_settings import CustomNavigationToolbar
        import inspect
        
        # Test 1: Check that context menu methods exist
        required_methods = [
            'show_line_context_menu',
            'show_annotation_context_menu'
        ]
        
        print("1. Checking method existence:")
        for method_name in required_methods:
            if hasattr(CustomNavigationToolbar, method_name):
                print(f"   ✓ {method_name} exists")
            else:
                print(f"   ✗ {method_name} missing")
                return False
        
        # Test 2: Check method signatures
        print("\n2. Checking method signatures:")
        
        # Check show_line_context_menu
        sig = inspect.signature(CustomNavigationToolbar.show_line_context_menu)
        params = list(sig.parameters.keys())
        if params == ['self', 'draggable_line', 'event']:
            print("   ✓ show_line_context_menu signature correct")
        else:
            print(f"   ✗ show_line_context_menu signature wrong: {params}")
            return False
        
        # Check show_annotation_context_menu
        sig = inspect.signature(CustomNavigationToolbar.show_annotation_context_menu)
        params = list(sig.parameters.keys())
        if params == ['self', 'draggable_annotation', 'event']:
            print("   ✓ show_annotation_context_menu signature correct")
        else:
            print(f"   ✗ show_annotation_context_menu signature wrong: {params}")
            return False
        
        # Test 3: Check method implementation details
        print("\n3. Checking implementation details:")
        
        # Check show_line_context_menu implementation
        source = inspect.getsource(CustomNavigationToolbar.show_line_context_menu)
        if 'QMenu(self)' in source:
            print("   ✓ show_line_context_menu uses toolbar as parent")
        else:
            print("   ✗ show_line_context_menu parent issue")
            return False
        
        if 'try:' in source and 'except' in source:
            print("   ✓ show_line_context_menu has error handling")
        else:
            print("   ✗ show_line_context_menu missing error handling")
            return False
        
        # Check show_annotation_context_menu implementation
        source = inspect.getsource(CustomNavigationToolbar.show_annotation_context_menu)
        if 'QMenu(self)' in source:
            print("   ✓ show_annotation_context_menu uses toolbar as parent")
        else:
            print("   ✗ show_annotation_context_menu parent issue")
            return False
        
        if 'try:' in source and 'except' in source:
            print("   ✓ show_annotation_context_menu has error handling")
        else:
            print("   ✗ show_annotation_context_menu missing error handling")
            return False
        
        print("\n" + "=" * 30)
        print("✅ All context menu fixes verified!")
        print("\nWhat was fixed:")
        print("• Context menus now use QMenu(self) instead of QMenu(mock_main_window)")
        print("• This prevents Qt widget type errors")
        print("• Both line and annotation context menus are fixed")
        print("• Proper error handling is in place")
        print("\nYour right-click context menus should now work without errors!")
        
        return True
        
    except Exception as e:
        print(f"\n✗ Context menu test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_context_menu_fixes()
