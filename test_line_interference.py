#!/usr/bin/env python3
"""
Test script to reproduce the line interference issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.features.line_drawing_manager import LineDrawingManager
from src.features.draggable_line import DraggableLine
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


class MockLabel:
    """Mock label for testing"""
    def __init__(self):
        self.text = ""
    
    def setText(self, text):
        self.text = text

class MockUI:
    """Mock UI for testing"""
    def __init__(self):
        self.lblLogInfo = MockLabel()

class MockMainWindow:
    """Mock main window for testing"""
    def __init__(self):
        self.current_canvas = None
        self.line_drawing_manager = None
        self.ui = MockUI()


def test_line_interference_issue():
    """Test the specific issue where creating a line near another line affects the existing line"""
    print("Testing line interference issue...")
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    # Step 1: Create first line with default properties (orange, solid, thickness 3)
    print("Step 1: Creating first line with default properties...")
    default_props = {
        'color': 'orange',
        'linewidth': 3,
        'linestyle': '-',
        'alpha': 1.0,
        'arrow_style': 'none'
    }
    manager.update_default_properties(**default_props)
    
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (1, 1))
    line1 = manager.finish_drawing((5, 5))
    
    print(f"Line1 created with properties: {line1.get_properties()}")
    
    # Verify line1 has the expected properties
    for key, expected_value in default_props.items():
        actual_value = line1.properties[key]
        assert actual_value == expected_value, f"Line1 {key}: expected {expected_value}, got {actual_value}"
    
    # Step 2: Change line1's style via right-click context menu (simulate)
    print("Step 2: Changing line1's style to red, dashed, thickness 1...")
    new_props = {
        'color': 'red',
        'linewidth': 1,
        'linestyle': '--',
        'alpha': 1.0
    }
    
    # Store original properties for comparison
    line1_original_props = line1.get_properties().copy()
    
    # Update line1's properties (simulating right-click context menu change)
    line1.update_properties(**new_props)
    
    print(f"Line1 after style change: {line1.get_properties()}")
    
    # Verify line1 has the new properties
    for key, expected_value in new_props.items():
        actual_value = line1.properties[key]
        assert actual_value == expected_value, f"Line1 after change {key}: expected {expected_value}, got {actual_value}"
    
    # Step 3: Create second line very near the first line
    print("Step 3: Creating second line very near the first line...")
    
    # Store line1's properties before creating line2
    line1_props_before_line2 = line1.get_properties().copy()
    print(f"Line1 properties before creating line2: {line1_props_before_line2}")
    
    # Create line2 very close to line1 (this should trigger the issue)
    manager.start_drawing(ax, (1.1, 1.1))  # Very close to line1's start point
    line2 = manager.finish_drawing((5.1, 5.1))  # Very close to line1's end point
    
    print(f"Line2 created with properties: {line2.get_properties()}")
    
    # Check line1's properties after creating line2
    line1_props_after_line2 = line1.get_properties().copy()
    print(f"Line1 properties after creating line2: {line1_props_after_line2}")
    
    # This is where the bug should manifest - line1's properties should NOT change
    properties_changed = False
    for key in new_props.keys():
        if line1_props_before_line2[key] != line1_props_after_line2[key]:
            print(f"BUG DETECTED: Line1 {key} changed from {line1_props_before_line2[key]} to {line1_props_after_line2[key]}")
            properties_changed = True
    
    if properties_changed:
        print("❌ BUG CONFIRMED: Creating line2 near line1 affected line1's properties!")
        return False
    else:
        print("✅ No interference detected - line1's properties remained unchanged")
    
    # Also check that line2 uses the updated defaults (red, dashed, thickness 1)
    print("Checking if line2 uses the updated defaults...")
    for key, expected_value in new_props.items():
        actual_value = line2.properties[key]
        if actual_value != expected_value:
            print(f"Line2 {key}: expected {expected_value}, got {actual_value}")
            print("❌ Line2 did not use the updated defaults")
            return False
    
    print("✅ Line2 correctly uses the updated defaults")
    return True


def test_double_click_near_existing_line():
    """Test double-clicking very close to an existing line"""
    print("Testing double-click near existing line...")
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    # Create first line
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (2, 2))
    line1 = manager.finish_drawing((6, 6))
    
    # Change line1's properties
    line1.update_properties(color='blue', linewidth=2, linestyle=':')
    line1_props_before = line1.get_properties().copy()
    
    # Simulate double-click very close to line1 (this should create a new line, not affect line1)
    close_point = (2.01, 2.01)  # Very close to line1's start point
    
    # Check if the point is detected as being on line1
    detected_line = manager.find_line_at_point(close_point)
    if detected_line == line1:
        print(f"Point {close_point} is detected as being on line1 (tolerance issue)")
    else:
        print(f"Point {close_point} is not detected as being on line1")
    
    # Create new line at the close point
    manager.start_drawing(ax, close_point)
    line2 = manager.finish_drawing((6.01, 6.01))
    
    # Check if line1's properties changed
    line1_props_after = line1.get_properties().copy()
    
    properties_changed = False
    for key in ['color', 'linewidth', 'linestyle']:
        if line1_props_before[key] != line1_props_after[key]:
            print(f"Line1 {key} changed from {line1_props_before[key]} to {line1_props_after[key]}")
            properties_changed = True
    
    if properties_changed:
        print("❌ Double-click near existing line affected the existing line!")
        return False
    else:
        print("✅ Double-click near existing line did not affect the existing line")
        return True


def run_all_tests():
    """Run all line interference tests"""
    print("Running Line Interference Tests...")
    print("=" * 60)
    
    try:
        test1_result = test_line_interference_issue()
        print()
        test2_result = test_double_click_near_existing_line()
        
        print("=" * 60)
        if test1_result and test2_result:
            print("✅ All line interference tests passed!")
            print("No interference detected between existing and new lines.")
        else:
            print("❌ Line interference issue detected!")
            print("Creating new lines near existing lines affects existing line properties.")
        
        return test1_result and test2_result
        
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
