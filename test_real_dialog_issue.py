#!/usr/bin/env python3
"""
Test script to reproduce the real dialog issue described by the user
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.features.line_drawing_manager import LineDrawingManager
from src.features.draggable_line import DraggableLine
from gui.dialog.line_settings_dialog import LineSettingsDialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


class MockLabel:
    def __init__(self):
        self.text = ""
    def setText(self, text):
        self.text = text

class MockUI:
    def __init__(self):
        self.lblLogInfo = MockLabel()

class MockMainWindow:
    def __init__(self):
        self.current_canvas = None
        self.line_drawing_manager = None
        self.ui = MockUI()


def test_real_user_workflow():
    """Test the exact workflow described by the user"""
    print("Testing real user workflow...")
    print("=" * 60)
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    print("Step 1: Create a line with default properties")
    
    # Create a line
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (1, 1))
    line1 = manager.finish_drawing((5, 5))
    
    line1.debug_properties("after creation")
    
    print("\nStep 2: Open settings dialog and change style")
    
    # Create and show dialog
    dialog1 = LineSettingsDialog(parent=None, line_manager=manager, current_line=line1)
    
    # Simulate user changes
    dialog1.color_edit.setText("#FF0000")  # Red
    dialog1.width_spin.setValue(3.0)       # Thickness 3
    dialog1.style_combo.setCurrentIndex(2) # Dotted (:)
    
    print("User set: Red color, thickness 3, dotted style")
    
    # Apply and close dialog
    dialog1.apply_settings()
    dialog1.close()
    
    line1.debug_properties("after first dialog")
    
    print("\nStep 3: Verify the changes were applied")
    props_after_first_dialog = line1.get_properties()
    
    expected_changes = {
        'color': '#FF0000',
        'linewidth': 3.0,
        'linestyle': ':'
    }
    
    for key, expected_value in expected_changes.items():
        actual_value = props_after_first_dialog[key]
        if actual_value != expected_value:
            print(f"❌ {key}: expected {expected_value}, got {actual_value}")
            return False
    
    print("✅ Changes applied correctly")
    
    print("\nStep 4: Open settings dialog again")
    
    # Create new dialog instance (simulating reopening)
    dialog2 = LineSettingsDialog(parent=None, line_manager=manager, current_line=line1)
    
    # Check what the dialog loaded
    loaded_color = dialog2.color_edit.text()
    loaded_width = dialog2.width_spin.value()
    loaded_style = dialog2.style_combo.currentIndex()
    
    print(f"Dialog reopened with: Color={loaded_color}, Width={loaded_width}, Style={loaded_style}")
    
    # Check if dialog loaded the correct values
    if loaded_color != '#FF0000':
        print(f"❌ BUG: Dialog color {loaded_color} != expected #FF0000")
        return False
    
    if loaded_width != 3.0:
        print(f"❌ BUG: Dialog width {loaded_width} != expected 3.0")
        return False
    
    if loaded_style != 2:  # Dotted style
        print(f"❌ BUG: Dialog style {loaded_style} != expected 2 (dotted)")
        return False
    
    print("✅ Dialog correctly loaded current line properties")
    
    print("\nStep 5: Test dragging the line")
    
    line1.debug_properties("before drag")
    
    # Simulate dragging
    line1.press = ((1, 1), 100, 100)
    line1.dragging_point = 'line'
    line1.connected = True
    
    # Simulate motion
    class MockEvent:
        def __init__(self, x, y):
            self.xdata = x
            self.ydata = y
    
    motion_event = MockEvent(2, 2)
    line1.handle_motion(motion_event)
    
    line1.debug_properties("after drag")
    
    # Check if properties changed during drag
    props_after_drag = line1.get_properties()
    
    for key, expected_value in expected_changes.items():
        actual_value = props_after_drag[key]
        if actual_value != expected_value:
            print(f"❌ BUG: After drag, {key}: expected {expected_value}, got {actual_value}")
            return False
    
    print("✅ Properties remained unchanged during dragging")
    
    print("\nStep 6: Open dialog again after dragging")
    
    # Create another dialog instance
    dialog3 = LineSettingsDialog(parent=None, line_manager=manager, current_line=line1)
    
    # Check what the dialog loaded after dragging
    loaded_color_after_drag = dialog3.color_edit.text()
    loaded_width_after_drag = dialog3.width_spin.value()
    loaded_style_after_drag = dialog3.style_combo.currentIndex()
    
    print(f"Dialog after drag: Color={loaded_color_after_drag}, Width={loaded_width_after_drag}, Style={loaded_style_after_drag}")
    
    # Check if dialog still loads the correct values
    if loaded_color_after_drag != '#FF0000':
        print(f"❌ BUG: After drag, dialog color {loaded_color_after_drag} != expected #FF0000")
        return False
    
    if loaded_width_after_drag != 3.0:
        print(f"❌ BUG: After drag, dialog width {loaded_width_after_drag} != expected 3.0")
        return False
    
    if loaded_style_after_drag != 2:
        print(f"❌ BUG: After drag, dialog style {loaded_style_after_drag} != expected 2")
        return False
    
    print("✅ Dialog correctly loaded properties even after dragging")
    
    return True


def test_property_persistence_across_operations():
    """Test that properties persist across various operations"""
    print("\nTesting property persistence across operations...")
    print("=" * 50)
    
    # Create a simple figure and canvas for testing
    fig = Figure(figsize=(8, 6))
    canvas = FigureCanvas(fig)
    ax = fig.add_subplot(111)
    
    # Create mock main window
    main_window = MockMainWindow()
    main_window.current_canvas = canvas
    
    # Create line manager
    manager = LineDrawingManager(main_window)
    main_window.line_drawing_manager = manager
    
    # Create a line
    manager.set_drawing_mode(True)
    manager.start_drawing(ax, (2, 2))
    line1 = manager.finish_drawing((6, 6))
    
    # Set custom properties
    custom_props = {
        'color': 'blue',
        'linewidth': 4,
        'linestyle': '--',
        'alpha': 0.7
    }
    
    line1.update_properties(**custom_props)
    print(f"Set custom properties: {custom_props}")
    
    # Test various operations that might affect properties
    operations = [
        ("update_artist", lambda: line1.update_artist()),
        ("connect/disconnect", lambda: (line1.disconnect(), line1.connect())),
        ("selection", lambda: (line1.set_selected(True), line1.set_selected(False))),
    ]
    
    for op_name, operation in operations:
        props_before = line1.get_properties()
        operation()
        props_after = line1.get_properties()
        
        # Check if any properties changed
        changed = []
        for key in custom_props.keys():
            if props_before[key] != props_after[key]:
                changed.append(f"{key}: {props_before[key]} -> {props_after[key]}")
        
        if changed:
            print(f"❌ {op_name} changed properties: {changed}")
            return False
        else:
            print(f"✅ {op_name} preserved properties")
    
    return True


def run_all_tests():
    """Run all real dialog issue tests"""
    print("Running Real Dialog Issue Tests...")
    print("=" * 70)
    
    try:
        test1_result = test_real_user_workflow()
        test2_result = test_property_persistence_across_operations()
        
        print("\n" + "=" * 70)
        if test1_result and test2_result:
            print("✅ ALL TESTS PASSED!")
            print("No real dialog issues detected in test environment.")
            print("\nIf you're still experiencing issues in the actual application,")
            print("the problem might be related to:")
            print("• Qt event handling in the real GUI")
            print("• Canvas refresh/redraw operations")
            print("• Signal/slot connections")
            print("• Threading or timing issues")
        else:
            print("❌ REAL DIALOG ISSUES DETECTED!")
            print("The dialog property persistence is not working correctly.")
        
        return test1_result and test2_result
        
    except Exception as e:
        print(f"❌ TESTS FAILED WITH EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
